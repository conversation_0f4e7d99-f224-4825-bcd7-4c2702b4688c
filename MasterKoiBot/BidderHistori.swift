//
//  BidderHistori.swift
// ikan
//
//  Created by <PERSON><PERSON> on 29/08/23.
//

import SwiftUI
import Alamofire
import SwiftyJSON
import WebKit
import Awesome


struct BidderHistori: View {
    @State private var showingAlert : Bool = false
    @State private var messageAlert : String = ""
    @State private var type : Int = 0
    @State private var isLoading = false
    
    @State private var searchText = ""
    @State private var dataItemList: [HistoriBid] = []
    
        @State private var keLogin : Bool = false;
    let columns = [
        GridItem(.flexible())
    ]
    var body: some View {
        NavigationStack{
            VStack {
                //NavigationLink("", isActive: $keLogin) { VerficationPhone(onCancel:{})}
                if dataItemList.isEmpty {
                    Spacer()
                    Text(messageAlert)
                        .foregroundColor(.gray)
                        .padding()
                    Spacer()
                } else {
                    ScrollView {
                        LazyVGrid(columns: columns) {
                            ForEach(dataItemList) { item in
                                NavigationLink {
                                    IkanDetail(id_obyek_lelang:item.id_obyek_lelang,title: "",judul_halaman: "")
                                } label: {
                                    HistoriBidView(item: item)
                                }
                                
                            }
                        }.padding(4)
                    }
                }
            }.navigationDestination(isPresented: $keLogin) { VerficationPhone(onCancel:{})}
        }.navigationBarTitle("Histori Bid", displayMode: .inline)
            .overlay {
                if isLoading {
                    ProgressView() // Menampilkan ProgressView saat isLoading = true
                }
            }.onAppear{
                getIkanLelang(searchText)
            }.alert(messageAlert, isPresented: $showingAlert) {
                Button("OK", role: .cancel) {
                    
                }
            }
    }
    func showDialogInfo(title: String, message: String, type: Int) {
        self.showingAlert = true
        self.messageAlert = message
        self.type = type
    }
    
    func getIkanLelang(_ query :String) {
        self.isLoading = true
         
        guard let url = URL(string: "\(FlavorStrings.baseURL)/api/bidder/history-bid") else {
            return
        }
         
        let session = loadPreference(kunciData: "session")
        let headers: HTTPHeaders = [
            "Session" : session
        ]
        
        dataItemList = []
        AF.request(url, method: .get,headers: headers).response{ response in
            switch response.result {
            case .success:
                self.isLoading = false
                let json = try? JSON(data: response.data! )
                if json?["meta"]["action"].stringValue == "login" {
                    keLogin = true
                }else if json?["meta"]["code"].intValue == 200 {
                    self.messageAlert = ""
                    let items = json?["data"].array
                    for item in items! {
                        
                        dataItemList.append(HistoriBid.init(
                            id_transaksi_bid : item["id_transaksi_bid"].stringValue,
                            id_buyer : item["id_buyer"].stringValue,
                            id_bid_group : item["id_bid_group"].stringValue,
                            id_obyek_lelang : item["id_obyek_lelang"].stringValue,
                            tgl_transaksi_bid : item["tgl_transaksi_bid"].stringValue,
                            token_transaksi_bid : item["token_transaksi_bid"].stringValue,
                            chat_transaksi_bid : item["chat_transaksi_bid"].stringValue,
                            list_transaksi_bid : item["list_transaksi_bid"].stringValue,
                            nilai_transaksi_bid : item["nilai_transaksi_bid"].stringValue,
                            buyer_transaksi_bid : item["buyer_transaksi_bid"].stringValue,
                            buyer_enkripsi_transaksi_bid : item["buyer_enkripsi_transaksi_bid"].stringValue,
                            input_timer : item["input_timer"].stringValue,
                            view_ikan_transaksi_bid : item["view_ikan_transaksi_bid"].stringValue,
                            view_foto : item["obyek_lelang"]["view_foto"].stringValue))
                    }
                    
                }else{
                    self.messageAlert = json?["meta"]["message"].stringValue ?? ""
                    //showDialogInfo(title: "Informasi", message: json?["meta"]["message"].stringValue ?? "", type: 1)
                }
                break
            case .failure:
                self.isLoading = false
                if let error = response.error, error._code == NSURLErrorNotConnectedToInternet {
                    self.showDialogInfo(title: "Informasi", message: "Tidak ada koneksi internet.", type: 1)
                } else {
                    self.showDialogInfo(title: "Informasi", message: "Gagal melakukan koneksi ke server", type: 1)
                }
                break
            }
        }
        
    }
}

struct HistoriBidView: View {
    let item: HistoriBid
    
    var body: some View {
        HStack {
            AsyncImage(url: URL(string: item.view_foto)) { phase in
                switch phase {
                case .empty:
                    Color("darkblue")
                    .overlay(
                    VStack{
                        Spacer()
                        ProgressView().progressViewStyle(CircularProgressViewStyle(tint: Color.white))
                        Spacer()
                    }).clipped()
                case .success(let image):
                    Color("darkblue")
                    .overlay(
                        image
                        .resizable()
                        .scaledToFit()
                    )
                    .clipped()
                case .failure:
                    VStack{
                        Spacer()
                        Image(systemName: "photo")
                        Spacer()
                    }
                @unknown default:
                    EmptyView()
                }
            }
                
            .frame(width: 70, height: 80)
            HTMLStringView(htmlContent: item.view_ikan_transaksi_bid)
            //HTMLText(html: item.view_ikan_transaksi_bid).lineLimit(3)
        }
        .background(Color.white)
        .cornerRadius(6)
        .shadow(color: Color.black.opacity(0.2), radius: 4, x: 0, y: 2)
    }
}
struct BidderHistori_Previews: PreviewProvider {
    static var previews: some View {
        BidderHistori()
    }
}


struct HistoriBid: Identifiable {
    let id = UUID()
    let id_transaksi_bid : String
    let id_buyer : String
    let id_bid_group : String
    let id_obyek_lelang : String
    let tgl_transaksi_bid : String
    let token_transaksi_bid : String
    let chat_transaksi_bid : String
    let list_transaksi_bid : String
    let nilai_transaksi_bid : String
    let buyer_transaksi_bid : String
    let buyer_enkripsi_transaksi_bid : String
    let input_timer : String
    let view_ikan_transaksi_bid : String
    let view_foto : String
}

struct HTMLStringView: UIViewRepresentable {
    let htmlContent: String

    func makeUIView(context: Context) -> WKWebView {
        let webView = WKWebView()
                webView.navigationDelegate = context.coordinator
                return webView
    }

    func updateUIView(_ uiView: WKWebView, context: Context) {
            // Set the default font size (you can adjust this as needed)
            let css = "body { font-size: 14px; }"
            let javascript = "var style = document.createElement('style'); style.innerHTML = '\(css)'; document.head.appendChild(style);"
            let userScript = WKUserScript(source: javascript, injectionTime: .atDocumentEnd, forMainFrameOnly: false)
            uiView.configuration.userContentController.addUserScript(userScript)

            // Enable auto line breaker
            let viewportScript = "var meta = document.createElement('meta'); meta.setAttribute('name', 'viewport'); meta.setAttribute('content', 'width=device-width'); document.getElementsByTagName('head')[0].appendChild(meta);"
            let viewportUserScript = WKUserScript(source: viewportScript, injectionTime: .atDocumentEnd, forMainFrameOnly: false)
            uiView.configuration.userContentController.addUserScript(viewportUserScript)

            uiView.loadHTMLString(htmlContent, baseURL: nil)
        }

        func makeCoordinator() -> Coordinator {
            Coordinator(self)
        }

        class Coordinator: NSObject, WKNavigationDelegate {
            var parent: HTMLStringView

            init(_ parent: HTMLStringView) {
                self.parent = parent
            }
        }
}


