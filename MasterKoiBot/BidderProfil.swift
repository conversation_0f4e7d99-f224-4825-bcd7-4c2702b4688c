//
//  BidderProfil.swift
// ikan
//
//  Created by <PERSON><PERSON> on 30/05/23.
//

import SwiftUI
import Alamofire
import SwiftyJSON
import Awesome

struct BidderProfil: View {
    @State private var isBlastEnabled = false
    @State private var namaBuyer = ""
    @State private var kodeBuyer = ""
    @State private var linkBuyer = ""
    @State private var alamatBuyer = ""
    @State private var kodePosBuyer = ""
    @State private var kotaBuyer = ""
    @State private var bank = ""
    @State private var noRekening = ""
    @State private var atasNama = ""
    
    @State private var showingAlert : Bool = false
    @State private var messageAlert : String = ""
    @State private var type : Int = 0
    @State private var isLoading = false
    @State private var isLoadingSimpan = false
    
    @State private var originaljson = JSON()
    
    
    @State private var keLogin : Bool = false;
    var body: some View {
        NavigationStack{
            ScrollView {
                
                //   NavigationLink("", isActive: $keLogin) { VerficationPhone(onCancel:{})}
                V<PERSON>(alignment: .leading){
                    Text("Nama")
                        .padding(.horizontal)
                        .font(.callout)
                        .font(Font.body.bold())
                    TextField("Nama", text: $namaBuyer)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .padding(.horizontal)
                        .disabled(true)
                    
                    Text("Kode")
                        .padding(.horizontal)
                        .font(.callout)
                        .font(Font.body.bold())
                    
                    TextField("Kode", text: $kodeBuyer)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .padding(.horizontal)
                        .disabled(true)
                    
                    Text("Link Buyer")
                        .padding(.horizontal)
                        .font(.callout)
                        .font(Font.body.bold())
                    
                    TextField("Link Buyer", text: $linkBuyer)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .padding(.horizontal)
                        .disabled(true)
                    
                    VStack(alignment: .leading){
                        Text("Alamat")
                            .padding(.horizontal)
                            .font(.callout)
                            .font(Font.body.bold())
                        
                        TextField("Alamat", text: $alamatBuyer)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .padding(.horizontal)
                        Text("Kode Pos")
                            .padding(.horizontal)
                            .font(.callout)
                            .font(Font.body.bold())
                        
                        TextField("Kode Pos", text: $kodePosBuyer)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .padding(.horizontal)
                        
                        Text("Kota")
                            .padding(.horizontal)
                            .font(.callout)
                            .font(Font.body.bold())
                        
                        TextField("Kota", text: $kotaBuyer)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .padding(.horizontal)
                    }
                    VStack(alignment: .leading){
                        Text("Bank")
                            .padding(.horizontal)
                            .font(.callout)
                            .font(Font.body.bold())
                        TextField("Bank", text: $bank)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .padding(.horizontal)
                        
                        Text("No Rekening")
                            .padding(.horizontal)
                            .font(.callout)
                            .font(Font.body.bold())
                        TextField("No Rekening", text: $noRekening)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .padding(.horizontal)
                        
                        Text("Atas Nama")
                            .padding(.horizontal)
                            .font(.callout)
                            .font(Font.body.bold())
                        TextField("Atas Nama", text: $atasNama)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .padding(.horizontal)
                        
                        
                        Toggle("Setuju berlangganan menerima pesan blast", isOn: $isBlastEnabled)
                            .padding(.horizontal)
                    }
                    
                    Button(action: {
                        simpanButtonTapped()
                    }) {
                        if self.isLoadingSimpan {
                            ProgressView()
                                .padding()
                                .frame(maxWidth: .infinity)
                        }else{
                            
                            HStack{
                                Image(uiImage: Awesome.Solid.save.asImage(size: 20,color: UIColor(Color.white)))
                                    .scaledToFit()
                                Text("Simpan").foregroundColor(Color.white)
                            }.frame(maxWidth: .infinity)
                                .padding(10)
                                .foregroundColor(.white)
                                .background(RoundedRectangle(cornerRadius: 32).foregroundColor(Color("darkblue"))).padding(.horizontal)
                            
                        }
                    }
                    
                    Spacer().frame(height: 150)
                }.navigationDestination(isPresented: $keLogin) { VerficationPhone(onCancel:{})}
            }.navigationTitle("Profil Bidder")
        }.overlay {
            if isLoading {
                ProgressView() // Menampilkan ProgressView saat isLoading = true
            }
        }.onAppear(){
            getProfil()
        }.alert(messageAlert, isPresented: $showingAlert) {
            Button("OK", role: .cancel) { }
        }
        
    }
    func simpanButtonTapped() {
        isLoadingSimpan = true;
        var parameters: [String: Any] = [:]
        
        /*
         if !namaBuyer.isEmpty && {
         parameters["nama_buyer"] = namaBuyer
         }
         
         if !kodeBuyer.isEmpty {
         parameters["kode_buyer"] = kodeBuyer
         }
         
         if !linkBuyer.isEmpty {
         parameters["link_buyer"] = linkBuyer
         }
         */
        
        if !alamatBuyer.isEmpty && alamatBuyer != originaljson["data"]["buyer"]["alamat_buyer"].stringValue {
            parameters["alamat_buyer"] = alamatBuyer
        }
        if !kodePosBuyer.isEmpty && kodePosBuyer != originaljson["data"]["buyer"]["kode_pos_buyer"].stringValue {
            parameters["kode_pos_buyer"] = kodePosBuyer
        }
        
        if !kotaBuyer.isEmpty && kotaBuyer != originaljson["data"]["buyer"]["kota_buyer"].stringValue{
            parameters["kota_buyer"] = kotaBuyer
        }
        
        if !bank.isEmpty && bank != originaljson["data"]["buyer"]["bank"].stringValue{
            parameters["bank"] = bank
        }
        
        if !noRekening.isEmpty && atasNama != originaljson["data"]["buyer"]["nomor_rekening"].stringValue{
            parameters["nomor_rekening"] = noRekening
        }
        
        if !atasNama.isEmpty && atasNama != originaljson["data"]["buyer"]["atas_nama"].stringValue{
            parameters["atas_nama"] = atasNama
        }
        
        parameters["terima_blast"] = isBlastEnabled ? 1 : 0
        
        guard let url = URL(string: "\(FlavorStrings.baseURL)/api/bidder/set") else {
            return
        }
         
        let session = loadPreference(kunciData: "session")
        let headers: HTTPHeaders = [
            "Session" : session
        ]
        
        AF.request(url,
                   method: .post,
                   parameters: parameters,
                   headers: headers).response { response in
            self.stopLoading()
            switch response.result {
            case .success:
                isLoadingSimpan = false;
                let json = try? JSON(data: response.data! )
                
                if json?["meta"]["action"].stringValue == "login" {
                    keLogin = true
                }else if json?["meta"]["code"].intValue == 200 {
                    showDialogInfo(title: "Informasi", message: (json?["meta"]["message"].stringValue)!, type: 1)
                } else {
                    showDialogInfo(title: "Informasi", message: (json?["meta"]["message"].stringValue)!, type: 0)
                }
            case .failure(_):
                isLoadingSimpan = false;
                if let error = response.error, error._code == NSURLErrorNotConnectedToInternet {
                    self.showDialogInfo(title: "Informasi", message: "Tidak ada koneksi internet.", type: 1)
                } else {
                    self.showDialogInfo(title: "Informasi", message: "Gagal melakukan koneksi ke server", type: 1)
                }
            }
        }
    }
    func getProfil() {
        isLoading = true;
        guard let url = URL(string: "\(FlavorStrings.baseURL)/api/profil") else {
            return
        }
         
        let session = loadPreference(kunciData: "session")
        let headers: HTTPHeaders = [
            "Session" : session
        ]
        
        AF.request(url, method: .get, headers: headers).response{ response in
            switch response.result {
            case .success:
                isLoading = false;
                let json = try? JSON(data: response.data! )
                self.originaljson = json ?? ""
                if json?["meta"]["action"].stringValue == "login" {
                    keLogin = true
                }else if json?["meta"]["code"].intValue == 200 {
                    namaBuyer = (json?["data"]["buyer"]["nama_buyer"].stringValue)!
                    kodeBuyer = (json?["data"]["buyer"]["kode_buyer"].stringValue)!
                    linkBuyer = (json?["data"]["buyer"]["link_buyer"].stringValue)!
                    alamatBuyer = (json?["data"]["buyer"]["alamat_buyer"].stringValue)!
                    kodePosBuyer = (json?["data"]["buyer"]["kode_pos_buyer"].stringValue)!
                    kotaBuyer = (json?["data"]["buyer"]["kota_buyer"].stringValue)!
                    bank = (json?["data"]["buyer"]["bank"].stringValue)!
                    noRekening = (json?["data"]["buyer"]["nomor_rekening"].stringValue)!
                    atasNama = (json?["data"]["buyer"]["atas_nama"].stringValue)!
                    
                    
                    if json?["data"]["buyer"]["terima_blast"].intValue == 1 {
                        isBlastEnabled = true
                    } else {
                        isBlastEnabled = false
                    }
                    
                } else {
                    showDialogInfo(title: "Informasi", message: (json?["meta"]["message"].stringValue)!,type: 0)
                    
                }
            case .failure(_):
                isLoading = false;
                if let error = response.error, error._code == NSURLErrorNotConnectedToInternet {
                    self.showDialogInfo(title: "Informasi", message: "Tidak ada koneksi internet.", type: 1)
                } else {
                    self.showDialogInfo(title: "Informasi", message: "Gagal melakukan koneksi ke server", type: 1)
                }
            }
        }
        
        
    }
    
    
    func startLoading() {
        // Implement loading indicator logic
    }
    
    func stopLoading() {
        isLoadingSimpan = false;
        // Implement loading indicator logic
    }
    
    func showDialogInfo(title: String, message: String, type: Int) {
        self.showingAlert = true
        self.messageAlert = message
        self.type = type
    }
}

struct BidderProfil_Previews: PreviewProvider {
    static var previews: some View {
        BidderProfil()
    }
}
