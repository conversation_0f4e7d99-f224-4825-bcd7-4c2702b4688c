//
//  BidderWishlist.swift
// ikan
//
//  Created by <PERSON><PERSON> on 2/24/24.
//

import Foundation
import SwiftUI
import Alamofire
import SwiftyJSON

struct BidderWishlist: View {
    @StateObject var wishlistManager = WishlistManager()
    @State private var showingAlert : Bool = false
    @State private var messageAlert : String = ""
    @State private var type : Int = 0
    @State private var isLoading = false
    @State private var searchText = ""
    @State private var dataItemList: [IkanLelang] = []
    
    @State private var keLogin : Bool = false;
    @State private var isFilterDialogVisible = false
    @State private var isApplyFilter = false
    @State var page = 1
    @State private var sudahdikirm : String = ""
    @State private var sudahdibayar : String = ""
    let adaptiveColumns = [GridItem(.flexible()), GridItem(.flexible()),]
    @State private var jumlahbelum = 0;
    
    func refreshData() async {
        getIkanLelang(self.searchText)
    }
    
    var body: some View {
        NavigationStack {
            ScrollView {
                V<PERSON> {
                    if dataItemList.isEmpty {
                        Spacer()
                        Text(messageAlert)
                            .foregroundColor(.gray)
                            .padding()
                        Spacer()
                    } else {
                        
                        LazyVGrid(columns: adaptiveColumns,alignment:.leading) {
                            ForEach(dataItemList) { item in
                                NavigationLink {
                                    IkanDetail(id_obyek_lelang:item.id_obyek_lelang,title: "",judul_halaman: "")
                                } label: {
                                    IkanLelangGridItem(item: item,halaman:"Bid Saya")
                                        .onAppear {
                                            if item.id == dataItemList.last?.id {
                                                page += 1
                                                getIkanLelang(self.searchText)
                                            }
                                        }
                                }
                            }
                        }
                    }
                }.padding(2).refreshable {
                    await refreshData()
                }
            }.navigationDestination(isPresented: $keLogin) { VerficationPhone(onCancel:{})}
        }
        .navigationBarTitle("Wishlist", displayMode: .inline)
        .overlay {
            if isLoading {
                ProgressView()
            }
        }.onAppear{
            wishlistManager.loadWishlist()
            getIkanLelang(searchText)
        }.alert(messageAlert, isPresented: $showingAlert) {
            Button("OK", role: .cancel) {
                
            }
        }
    }
    func showDialogInfo(title: String, message: String, type: Int) {
        self.showingAlert = true
        self.messageAlert = message
        self.type = type
    }
    func ChatSeller(ikanItem : IkanLelang){
        let urlWhats =
        "https://api.whatsapp.com/send/?phone="
        + ikanItem.no_hp_seller + "&text=Hai Admin "
        + ikanItem.nama_merchant_seller + ", saya mau tanya ikan ini : \n\n"
        + ikanItem.nama_obyek_lelang + "\n"
        + ikanItem.short_link_obyek_lelang + "&type=phone_number&app_absent=0";
        
        if let urlString = urlWhats.addingPercentEncoding(withAllowedCharacters: CharacterSet.urlQueryAllowed){
            if let whatsappURL = URL(string: urlString) {
                if UIApplication.shared.canOpenURL(whatsappURL){
                    if #available(iOS 10.0, *) {
                        UIApplication.shared.open(whatsappURL, options: [:], completionHandler: nil)
                    } else {
                        UIApplication.shared.openURL(whatsappURL)
                    }
                }
                else {
                    showDialogInfo(title: "Informasi", message: "Whatsapp tidak terinstall di Iphone ini.",type: 1)
                }
            }
        }
    }
    func ChatBantuan(ikanItem : IkanLelang){
        let urlWhats =
        "https://api.whatsapp.com/send/?phone="
        + ikanItem.no_hp_marketing + "&text=Hai Pusat Bantuan, saya mohon bantuan terkait ikan ini : \n\n"
        + ikanItem.nama_obyek_lelang + "\n"
        + ikanItem.short_link_obyek_lelang + "&type=phone_number&app_absent=0";
        
        if let urlString = urlWhats.addingPercentEncoding(withAllowedCharacters: CharacterSet.urlQueryAllowed){
            if let whatsappURL = URL(string: urlString) {
                if UIApplication.shared.canOpenURL(whatsappURL){
                    if #available(iOS 10.0, *) {
                        UIApplication.shared.open(whatsappURL, options: [:], completionHandler: nil)
                    } else {
                        UIApplication.shared.openURL(whatsappURL)
                    }
                }
                else {
                    showDialogInfo(title: "Informasi", message: "Whatsapp tidak terinstall di Iphone ini.",type: 1)
                }
            }
        }
    }
    
    func ChatWinner(ikanItem : IkanLelang){
        let urlWhats =
        "https://api.whatsapp.com/send/?phone="
        + ikanItem.no_hp_buyer + "&text=Hai "
        + ikanItem.nama_buyer + ", saya ingin menindaklanjuti ikan ini : \n\n"
        + ikanItem.nama_obyek_lelang + "\n"
        + ikanItem.short_link_obyek_lelang + "\n yang telah Anda menangkan&type=phone_number&app_absent=0";
        
        if let urlString = urlWhats.addingPercentEncoding(withAllowedCharacters: CharacterSet.urlQueryAllowed){
            if let whatsappURL = URL(string: urlString) {
                if UIApplication.shared.canOpenURL(whatsappURL){
                    if #available(iOS 10.0, *) {
                        UIApplication.shared.open(whatsappURL, options: [:], completionHandler: nil)
                    } else {
                        UIApplication.shared.openURL(whatsappURL)
                    }
                }
                else {
                    showDialogInfo(title: "Informasi", message: "Whatsapp tidak terinstall di Iphone ini.",type: 1)
                }
            }
        }
        
    }
    
    func getIkanLelang(_ query :String) {
        if self.page == 1 {
            dataItemList = []
            self.isLoading = true
        }
        guard let url = URL(string: "\(FlavorStrings.baseURL)/api/ikan/check-live/"+wishlistManager.getWishlistAsString(separator: ",")) else {
            return
        }
         
        let session = loadPreference(kunciData: "session")
        let headers: HTTPHeaders = [
            "Session" : session
        ]
        
        let parameters: Parameters = [
            "search": wishlistManager.getWishlistAsString(separator: ","),
            "page": page,
        ]
        AF.request(url, method: .get,parameters: parameters,headers: headers).response{ response in
            switch response.result {
            case .success:
                self.isLoading = false
                let json = try? JSON(data: response.data! )
                if json?["meta"]["action"].stringValue == "login" {
                    keLogin = true
                }else if json?["meta"]["code"].intValue == 200 {
                    self.messageAlert = ""
                    if let items = json?["data"].array {
                        for item in items {
                            dataItemList.append(
                                IkanLelang.init(item: item)
                            )
                            wishlistManager.addToWishlist(item["id_obyek_lelang"].stringValue)
                        }
                    }
                }else{
                    self.messageAlert = json?["meta"]["message"].stringValue ?? ""
                    //showDialogInfo(title: "Informasi", message:json?["meta"]["message"].stringValue ?? "", type: 1)
                }
                break
            case .failure:
                self.isLoading = false
                if let error = response.error, error._code == NSURLErrorNotConnectedToInternet {
                    self.showDialogInfo(title: "Informasi", message: "Tidak ada koneksi internet.", type: 1)
                } else {
                    self.showDialogInfo(title: "Informasi", message: "Gagal melakukan koneksi ke server", type: 1)
                }
                break
            }
        }
        
    }
}
