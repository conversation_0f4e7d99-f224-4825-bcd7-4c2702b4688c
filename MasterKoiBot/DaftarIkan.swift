//
//  BidderIkanSaya.swift
// ikan
//
//  Created by <PERSON><PERSON> on 06/06/23.
//

import SwiftUI
import Alamofire
import SwiftyJSON

struct DaftarIkan: View {
    var id : String
    var aksi : Int
    var title : String
    var judul_halaman : String
    var api : String
    var cari : Bool
    @State private var showingAlert : Bool = false
    @State private var messageAlert : String = ""
    @State private var type : Int = 0
    @State private var isLoading = false
    @State private var isPaginating = false
    @State private var searchText = ""
    @State private var dataItemList: [IkanLelang] = []
    
    @State private var keLogin : Bool = false;
    @State private var isFilterDialogVisible = false
    @State private var isApplyFilter = false
    @State var page = 1
    @State private var sudahdikirm : String = ""
    @State private var sudahdibayar : String = ""
    let adaptiveColumns = [GridItem(.flexible()), GridItem(.flexible()),]
    @State private var jumlahbelum = 0;
    
    func refreshData() async {
        getIkanLelang(self.searchText)
    }
    var body: some View {
        NavigationStack{
            VStack {
                if (title == "Ikan Saya"){
                    WarningMessageView()
                }
                if cari {
                    HStack {
                        Image(systemName: "magnifyingglass")
                        
                        TextField("Search", text: $searchText, onCommit: {
                            page = 1
                            getIkanLelang(searchText)
                        })
                        
                        Button(action: {
                            searchText = ""
                            page = 1
                            getIkanLelang(searchText)
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .opacity(searchText.isEmpty ? 0 : 1)
                        }
                        /*Button(action: {
                         isFilterDialogVisible = true
                         }) {
                         HStack{
                         Image(systemName: "slider.horizontal.3")
                         Text("Filter").foregroundColor(Color("darkblue"))
                         }
                         }.sheet(isPresented: $isFilterDialogVisible) {
                         FilterDialogViewIkanTerjual(
                         isVisible: $isFilterDialogVisible,
                         sudahdikirm: $sudahdikirm,
                         sudahdibayar: $sudahdibayar,
                         onReset : resetFilter,
                         onApplyFilter: ApplyFilter,
                         isApplyFilter: $isApplyFilter
                         ).presentationDetents([.medium])
                         }*/
                    }.padding(8)
                        .background(Color.white)
                        .cornerRadius(4)
                        .shadow(color: Color.black.opacity(0.2), radius: 2, x: 0, y: 2)
                        .padding(4)
                }
                ScrollView {
                    if dataItemList.isEmpty {
                        Spacer()
                        Text(messageAlert)
                            .foregroundColor(.gray)
                            .multilineTextAlignment(.center)
                            .padding()
                        Spacer()
                    } else {
                        if (title == "Ikan Saya"){
                            if jumlahbelum > 0 {
                            HStack{
                                Text("Poin")
                                    .foregroundColor(Color(hex:"#B71C1C"))
                                    .font(.system(size: 20))
                                    .bold()
                                    .padding(2)
                                VStack(alignment: .leading){
                                    Text("\(jumlahbelum) Ekor belum diulas").bold()
                                    Text("Segera berikan rating penilaian Anda, dan dapatkan bonus POIN yang dapat ditukar dengan hadiah menarik.")
                                }
                            }.padding()
                                .background(Color(hex:"#F7EBEC"))
                                .cornerRadius(3)
                                .shadow(radius: 2)
                                .padding(2)
                        }
                    }
                        LazyVGrid(columns: adaptiveColumns,alignment:.leading) {
                            ForEach(dataItemList) { item in
                                NavigationLink {
                                    IkanDetail(id_obyek_lelang:item.id_obyek_lelang,title: title,judul_halaman: judul_halaman)
                                } label: {
                                    IkanLelangGridItem(item: item,halaman:title)
                                        .onAppear {
                                            if item.id == dataItemList.last?.id {
                                                page += 1
                                                getIkanLelang(self.searchText)
                                            }
                                        }
                                }
                            }
                            if isPaginating {
                                ProgressView() // Progress indicator di bagian bawah saat memuat halaman berikutnya
                                    .frame(maxWidth: .infinity)
                                    .padding()
                                ProgressView() // Progress indicator di bagian bawah saat memuat halaman berikutnya
                                    .frame(maxWidth: .infinity)
                                    .padding()
                            }
                        }
                    }
                }.padding(2).refreshable {
                    await refreshData()
                }
                if loadPreference(kunciData: "as_bnr") == "1" {
                    Button(action:{
                        let urlWhats = "https://api.whatsapp.com/send/?phone=" + loadPreference(kunciData: "nomor_bantuan") + "&text=Saya dilaporkan sebagai pelaku BNR, Mohon bantuan mediasinya.&type=phone_number&app_absent=0"
                        if let urlString = urlWhats.addingPercentEncoding(withAllowedCharacters: CharacterSet.urlQueryAllowed){
                            if let whatsappURL = URL(string: urlString) {
                                if UIApplication.shared.canOpenURL(whatsappURL){
                                    if #available(iOS 10.0, *) {
                                        UIApplication.shared.open(whatsappURL, options: [:], completionHandler: nil)
                                    } else {
                                        UIApplication.shared.openURL(whatsappURL)
                                    }
                                }
                                else {
                                    showDialogInfo(title: "Informasi", message: "Whatsapp tidak terinstall di Iphone ini.",type: 1)
                                }
                            }
                        }
                    }) {
                        HStack {
                            Spacer()
                            Image(systemName: "message")
                            Text("Anda dilaporkan BNR, klik info selengkpanya").font(.system(size: 20))
                            Spacer()
                        }
                        .padding()
                        .foregroundColor(.white)
                        .background(RoundedRectangle(cornerRadius: 32).foregroundColor(Color.red))
                        
                    }.padding(.horizontal,4).padding(.top, -10)
                }
            }.navigationDestination(isPresented: $keLogin) { VerficationPhone(onCancel:{})}
        }.navigationBarTitle(judul_halaman, displayMode: .inline)
            .overlay {
                if isLoading {
                    ProgressView() // Menampilkan ProgressView saat isLoading = true
                }
            }.onAppear{
                getIkanLelang(searchText)
                if(title == "Ikan Saya"){
                    getIkanbelumdiUlas()
                }
            }.alert(messageAlert, isPresented: $showingAlert) {
                Button("OK", role: .cancel) {
                    
                }
            }
    }
    func showDialogInfo(title: String, message: String, type: Int) {
        self.showingAlert = true
        self.messageAlert = message
        self.type = type
    }
    func getIkanbelumdiUlas(){
        guard let url = URL(string: "\(FlavorStrings.baseURL)/api/bidder/ikan-saya/belum-ulasan") else {
            return
        }
        let session = loadPreference(kunciData: "session")
        let headers: HTTPHeaders = [
            "Session" : session
        ]
        
        AF.request(url, method: .get,headers: headers).response{ response in
            switch response.result {
            case .success:
                isLoading = false
                let json = try? JSON(data: response.data! )
                if json?["meta"]["action"].stringValue == "login" {
                    keLogin = true
                }else if json?["meta"]["code"].intValue == 200 {
                    self.messageAlert = ""
                    
                    let items = json?["data"].array
                    jumlahbelum = items?.count ?? 0
                }
                break
            case .failure:
                isLoading = false
                if let error = response.error, error._code == NSURLErrorNotConnectedToInternet {
                    //self.showDialogInfo(title: "Informasi", message: "Tidak ada koneksi internet.", type: 1)
                } else {
                    //self.showDialogInfo(title: "Informasi", message: "Gagal melakukan koneksi ke server", type: 1)
                }
                break
            }
        }
        
    }
    
    func getIkanLelang(_ query :String) {
        if self.page == 1 {
            dataItemList = []
            self.isLoading = true
        } else {
            self.isPaginating = true
        }
        
        guard let url = URL(string: "\(FlavorStrings.baseURL)/api/"+api) else {
            return
        }
         
        let session = loadPreference(kunciData: "session")
        let headers: HTTPHeaders = [
            "Session" : session
        ]
        
        let parameters: Parameters = [
            "search": query,
            "page": page,
            
        ]
        AF.request(url, method: .get,parameters: parameters,headers: headers).response{ response in
            switch response.result {
            case .success:
                let json = try? JSON(data: response.data! )
                if json?["meta"]["action"].stringValue == "login" {
                    keLogin = true
                }else if json?["meta"]["code"].intValue == 200 {
                    self.messageAlert = ""
                    
                    if let items = json?["data"].array {
                        for item in items {
                            dataItemList.append(
                                IkanLelang.init(item: item)
                            )
                        
                        }
                    }
                }else{
                    self.messageAlert = json?["meta"]["message"].stringValue ?? ""
                }
                break
            case .failure:
                if let error = response.error, error._code == NSURLErrorNotConnectedToInternet {
                    self.showDialogInfo(title: "Informasi", message: "Tidak ada koneksi internet.", type: 1)
                } else {
                    self.showDialogInfo(title: "Informasi", message: "Gagal melakukan koneksi ke server", type: 1)
                }
            }
            self.isLoading = false
            self.isPaginating = false
        }
    }
}
