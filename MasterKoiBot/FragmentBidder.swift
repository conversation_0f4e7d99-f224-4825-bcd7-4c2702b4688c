//
//  FragmentBidder.swift
// ikan
//
//  Created by <PERSON><PERSON> on 31/05/23.
//

import SwiftUI
import Awesome
import Alamofire
import SwiftyJSON

struct FragmentBidder: View {
    @State private var isBuyer: Bool = false
    
    @State private var titleAlert : String = ""
    @State private var messageAlert : String = ""
    
    @State private var confrimAlert : Bool = false
    @State private var loadingBid : Bool = false
    @State private var showingAlert : Bool = false
    @State private var type : Int = 0
    
    @State private var loadingPoin : Bool = false
    
    @State private var view_summary_point_reward : String = ""
    @State private var summary_point_reward : Float = 0
    @State private var view_saldo_point_reward : String = ""
    @State private var saldo_point_reward : Float = 0
    
    @State private var id_point_level : Int = 0
    @State private var min_point_level : Int = 0
    @State private var max_point_level : Int = 0
 
    @State private var nama_point_level : String = ""
    @State private var icon_point_level : String = ""
    
    @State private var kupon : String = ""
    @State private var warningkupon : String = ""
    
    
    
    @State private var keDetailIkan = false
    @State private var id_obyek_lelang: String = ""
    @State private var isWebViewRefreshed = false
    @State private var isLoading = false
    @State private var progress: Float = 0.0
    @State private var isNetworkAvailable = true
    @Binding var halamanWeb : Bool
    @Binding var selection : Int
    @State var url: String = ""
    
    //@State private var reklameList: [Reklame] = []
    var body: some View {
        NavigationStack{
            VStack{
                WarningMessageView().padding(.bottom, -8)
                ZStack{
                    ScrollView {
                        VStack {
                            if halamanWeb == false {
                                ReklameLayoutView().padding(.bottom, -8)
                                ZStack {
                                    Image(FlavorStrings.gambar(no: "3"))
                                        .resizable()
                                        .scaledToFill()
                                        .frame(height: 160)
                                        .clipped()
                                    
                                    Color.black.opacity(0.6)
                                        .frame(height: 160)
                                    
                                    VStack {
                                        if isBuyer {
                                            HStack{
                                                VStack{
                                                    Text(loadPreference(kunciData: "nama_buyer"))
                                                        .fontWeight(.bold)
                                                        .foregroundColor(.white)
                                                        .multilineTextAlignment(.center)
                                                    Text(loadPreference(kunciData: "no_hp_buyer"))
                                                        .foregroundColor(.white)
                                                        .multilineTextAlignment(.center)
                                                        .onAppear(){getPoin()}
                                                }.padding()
                                                VStack{
                                                    if loadingPoin {
                                                        ProgressView().progressViewStyle(CircularProgressViewStyle(tint: Color.white))
                                                    }else{
                                                        NavigationLink {
                                                            PoinHistori()
                                                        } label: {
                                                            VStack{
                                                                AsyncImage(url: URL(string:  icon_point_level)){phase in
                                                                    switch phase {
                                                                    case .success(let image):
                                                                        image
                                                                            .resizable()
                                                                            .scaledToFit()
                                                                            .clipped()
                                                                            .frame(width:80,height: 80)
                                                                    case .failure:
                                                                        Image(systemName: "photo")
                                                                            .resizable()
                                                                            .frame(width:80,height: 80)
                                                                        
                                                                    case .empty:
                                                                        ProgressView().progressViewStyle(CircularProgressViewStyle(tint: Color.white)).frame(width:80,height: 80)
                                                                    @unknown default:
                                                                        EmptyView()
                                                                    }
                                                                }
                                                                
                                                                Label(view_saldo_point_reward, systemImage: "eye")
                                                                    .font(.caption)
                                                                    .padding(.top, -10)
                                                                    .foregroundColor(.white)
                                                                    .lineLimit(1)
                                                            }
                                                        }
                                                    }
                                                }.padding()
                                            }
                                            
                                        }else{
                                            Text("Terintegrasi Real-Time antara WhatsApp dan Telegram, serta terpublis di Official Web 😎")
                                                .font(.title2)
                                                .foregroundColor(.white)
                                                .multilineTextAlignment(.center)
                                                .padding()
                                        }
                                    }
                                }.padding(.bottom, -8)
                            }
                            if loadPreference(kunciData: "as_bnr") == "1" {
                                Button(action:{
                                    let urlWhats = "https://api.whatsapp.com/send/?phone=" + loadPreference(kunciData: "nomor_bantuan") + "&text=Saya dilaporkan sebagai pelaku BNR, Mohon bantuan mediasinya.&type=phone_number&app_absent=0"
                                    if let urlString = urlWhats.addingPercentEncoding(withAllowedCharacters: CharacterSet.urlQueryAllowed){
                                        if let whatsappURL = URL(string: urlString) {
                                            if UIApplication.shared.canOpenURL(whatsappURL){
                                                if #available(iOS 10.0, *) {
                                                    UIApplication.shared.open(whatsappURL, options: [:], completionHandler: nil)
                                                } else {
                                                    UIApplication.shared.openURL(whatsappURL)
                                                }
                                            }
                                            else {
                                                showDialogInfo(title: "Informasi", message: "Whatsapp tidak terinstall di Iphone ini.",type: 1)
                                            }
                                        }
                                    }
                                }) {
                                    HStack {
                                        Spacer()
                                        Image(systemName: "message")
                                        Text("Anda dilaporkan BNR, klik info selengkapnya")
                                            .bold()
                                            .font(.system(size: 12))
                                            .multilineTextAlignment(.center)
                                        Spacer()
                                    }
                                    .padding()
                                    .foregroundColor(.white)
                                    .background(RoundedRectangle(cornerRadius: 32).foregroundColor(Color.red))
                                    
                                }.padding(4)
                            }
                            // Replace with your RecyclerView equivalent in SwiftUI
                            VStack{
                                ZStack{
                                    NavigationLink(destination: menuKlik(namaMenu: "Undian Bulanan",grupMenu: "Bidder") , label: {
                                        HStack{
                                            VStack {
                                                if kupon.count > 2 {
                                                    EmptyView().padding(.horizontal)
                                                }else{
                                                    HStack {
                                                        Spacer()
                                                    }
                                                }
                                                Image(uiImage: Awesome.Solid.gift.asImage(size: 40.0,color: UIColor(Color("darkblue")))).resizable()
                                                    .scaledToFit()
                                                    .tint(Color("darkblue"))
                                                    .foregroundColor(Color("darkblue"))
                                                    .frame(height: 24)
                                                Text("Undian Bulanan")
                                                    .foregroundColor(Color("darkblue"))
                                                    .font(.caption2)
                                                    .lineLimit(2)
                                                    .padding(.top, 4)
                                                
                                                Spacer()
                                            }.padding()
                                            
                                            if kupon.count > 2 {
                                                Spacer()
                                                VStack(alignment: .leading) {
                                                    Text(kupon)
                                                        .multilineTextAlignment(.leading)
                                                        .foregroundColor(Color("darkblue"))
                                                    Text(warningkupon)
                                                        .multilineTextAlignment(.leading)
                                                        .foregroundColor(Color("darkblue"))
                                                        .font(.caption)
                                                    
                                                }
                                            }
                                        }
                                        .padding(6)
                                        .background(Color.white)
                                        .cornerRadius(8)
                                        .shadow(radius: 2)
                                        .padding(1)
                                    }
                                    )
                                    VStack {
                                        HStack {
                                            Image("new_pita").resizable().scaledToFit().frame(height: 60)
                                            Spacer()
                                        }
                                        Spacer()
                                    }.padding(1)
                                }
                                HStack{
                                    CardViewMenuItem(
                                        icon: Awesome.Solid.poll.asImage(size: 40.0,color: UIColor(Color("darkblue"))),
                                        text: "Dashboard Bidder",
                                        grup: "Bidder")
                                    
                                    CardViewMenuItem(
                                        icon: Awesome.Solid.userCog.asImage(size: 40.0,color: UIColor(Color("darkblue"))),
                                        text: "Profil Bidder",
                                        grup: "Seller")
                                    CardViewMenuItemWeview(
                                        icon: Awesome.Solid.medal.asImage(size: 40.0,color: UIColor(Color("darkblue"))),
                                        text: "Poin",
                                        grup: "Bidder",
                                        halamanWeb: $halamanWeb,
                                        url: $url,
                                        isLoading: $isLoading, selection: $selection)
                                }
                                HStack{
                                    CardViewMenuItem(
                                        icon: Awesome.Solid.gavel.asImage(size: 40.0,color: UIColor(Color("darkblue"))),
                                        text: "BID Saya",
                                        grup: "Seller")
                                    
                                    CardViewMenuItem(
                                        icon: Awesome.Solid.fish.asImage(size: 40.0,color: UIColor(Color("darkblue"))),
                                        text: "Ikan Saya",
                                        grup: "Seller")
                                    
                                    CardViewMenuItem(
                                        icon: Awesome.Solid.heart.asImage(size: 40.0,color: UIColor(Color("darkblue"))),
                                        text: "Wishlist",
                                        grup: "Seller")
                                }
                                HStack{
                                    CardViewMenuItem(
                                        icon: Awesome.Solid.history.asImage(size: 40.0,color: UIColor(Color("darkblue"))),
                                        text: "Histori BID",
                                        grup: "Seller")
                                    CardViewMenuItemWeview(
                                        icon: Awesome.Solid.creditCard.asImage(size: 40.0,color: UIColor(Color("darkblue"))),
                                        text: "Rekening Penjamin",
                                        grup: "Seller",
                                        halamanWeb: $halamanWeb,
                                        url: $url,
                                        isLoading: $isLoading, selection: $selection)
                                    CardViewMenuItemWeview(
                                        icon: Awesome.Solid.unlink.asImage(size: 40.0,color: UIColor(Color("darkblue"))),
                                        text: "BNR",
                                        grup: "Seller",
                                        halamanWeb: $halamanWeb,
                                        url: $url,
                                        isLoading: $isLoading, selection: $selection)
                                }
                                HStack{
                                    CardViewMenuItemWeview(
                                        icon: Awesome.Solid.creditCard.asImage(size: 40.0,color: UIColor(Color("darkblue"))),
                                        text: "Ikan BNR",
                                        grup: "Bidder",
                                        halamanWeb: $halamanWeb,
                                        url: $url,
                                        isLoading: $isLoading, selection: $selection)
                                }
                                
                                
                            }.padding(8)
                        }
                    }
                    if halamanWeb {
                        ZStack {
                            WebView(
                                url: $url,
                                isLoading: $isLoading,
                                isNetworkAvailable: $isNetworkAvailable,
                                id_obyek_lelang: $id_obyek_lelang,
                                keDetailIkan: $keDetailIkan,
                                isWebViewRefreshed: $isWebViewRefreshed)
                            if isLoading {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle())
                                    .frame(width: 200, height: 200)
                                    .padding()
                            }
                            
                        }
                        /*.background(
                         NavigationLink( destination: IkanDetail(id_obyek_lelang: id_obyek_lelang), isActive: $keDetailIkan,label: {EmptyView()})
                         )
                         */
                    }
                }
            }.navigationDestination(isPresented: $keDetailIkan) {IkanDetail(id_obyek_lelang: id_obyek_lelang,title: ",",judul_halaman: "")}
        }.alert(messageAlert, isPresented: $showingAlert) {
            Button("OK", role: .cancel) {
            }
        }.onAppear(perform: {
            //getReklame()
            halamanWeb = false
            self.isBuyer =  loadPreference(kunciData: "list_buyer").count > 1
        })
    }
    
    
    func getPoin() {
        self.loadingPoin = true
        guard let url = URL(string: "\(FlavorStrings.baseURL)/api/poin") else {
            return
        }
         
        let session = loadPreference(kunciData: "session")
        let headers: HTTPHeaders = [
            "Session" : session
        ]
        AF.request(url, method: .get,headers: headers).response{ response in
            switch response.result {
            case .success:
                self.loadingPoin = false
                let json = try? JSON(data: response.data! )
                if json?["meta"]["action"].stringValue == "login" {
                    //    keLogin = true
                }else if json?["meta"]["code"].intValue == 200 {
                    summary_point_reward = (json?["data"]["summary_point_reward"].floatValue)!
                    view_summary_point_reward = json?["data"]["view_summary_point_reward"].stringValue ?? ""
                    saldo_point_reward = (json?["data"]["saldo_point_reward"].floatValue)!
                    view_saldo_point_reward = json?["data"]["view_saldo_point_reward"].stringValue ?? ""
                    
                    id_point_level = (json?["data"]["ref_point_level"]["id_point_level"].intValue)!
                    min_point_level = (json?["data"]["ref_point_level"]["min_point_level"].intValue)!
                    max_point_level = (json?["data"]["ref_point_level"]["max_point_level"].intValue)!
                    self.nama_point_level = json?["data"]["ref_point_level"]["nama_point_level"].stringValue ?? ""
                    self.icon_point_level = json?["data"]["ref_point_level"]["icon_point_level"].stringValue ?? ""
                  
                    
                    kupon = json?["data"]["view_total_kupon_bulan_ini"].stringValue ?? ""
                    warningkupon = json?["data"]["warning_kupon"].stringValue ?? ""
                    //binding.imageViewKupun.setColorFilter(Color.parseColor("#FFD700"));
                                               
                }else{
                    view_saldo_point_reward = json?["meta"]["message"].stringValue ?? ""
                    kupon = ""
                    warningkupon = ""
                }
                break
            case .failure(_):
                self.loadingPoin = false
                view_saldo_point_reward = response.description
                kupon = ""
                warningkupon = ""
            }
        }
        
    }
    func showDialogInfo(title: String, message: String, type: Int) {
        self.showingAlert = true
        self.messageAlert = message
        self.type = type
    }
    func onclikSosmed(sosmed: String) {
        if let urlString = sosmed.addingPercentEncoding(withAllowedCharacters: CharacterSet.urlQueryAllowed){
            if let whatsappURL = URL(string: urlString) {
                if UIApplication.shared.canOpenURL(whatsappURL){
                    if #available(iOS 10.0, *) {
                        UIApplication.shared.open(whatsappURL, options: [:], completionHandler: nil)
                    } else {
                        UIApplication.shared.openURL(whatsappURL)
                    }
                }else {
                    showDialogInfo(title: "Informasi", message: "Belum disetting",type: 1)
                }
            }else {
                showDialogInfo(title: "Informasi", message: "Belum disetting",type: 1)
            }
        }else {
            showDialogInfo(title: "Informasi", message: "Belum disetting",type: 1)
        }
    }
    /*
    func getReklame(){
        reklameList.removeAll()
        
         
        guard let url = URL(string: "\(FlavorStrings.baseURL)/api/reklame") else {
            return
        }
         
        let session = loadPreference(kunciData: "session")
        let headers: HTTPHeaders = [
            "Session" : session
        ]
        
        let parameters: Parameters = [
            "id_lelang":"",
           // "id_obyek_lelang":ikan_lelang.id_obyek_lelang
        ]
        
        AF.request(url, method: .get,parameters: parameters,headers: headers).response{ response in
            switch response.result {
            case .success:
                self.isLoading = false
                let json = try? JSON(data: response.data! )
                if json?["meta"]["action"].stringValue == "login" {
                    //keLogin = true
                }else if json?["meta"]["code"].intValue == 200 {
                    self.messageAlert = ""
                    let items = json?["data"].array
                    if items != nil {
                        for item in items! {
                            reklameList.append(
                                Reklame.init(item: item)
                            )
                            
                        }
                    }
                    reklameList.shuffle()
                    
                }else{
                    self.messageAlert = json?["meta"]["message"].stringValue ?? ""
                    
                }
                break
            case .failure:
                self.isLoading = false
                if let error = response.error, error._code == NSURLErrorNotConnectedToInternet {
                    self.showDialogInfo(title: "Informasi", message: "Tidak ada koneksi internet.", type: 1)
                } else {
                    self.showDialogInfo(title: "Informasi", message: "Gagal melakukan koneksi ke server", type: 1)
                }
                break
            }
        }
        
    }
     */
}



struct FragmentBidder_Previews: PreviewProvider {
    static var previews: some View {
        //FragmentBidder()
        Text("a")
    }
}
