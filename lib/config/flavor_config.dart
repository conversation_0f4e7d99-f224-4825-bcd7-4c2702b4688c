enum Flavor {
  koifish,
  goldfish,
  arwanafish,
}

class FlavorConfig {
  final Flavor flavor;
  final String name;
  final String appId;
  final String appStoreId;
  final String appName;
  final String baseUrl;
  final String privacyPolicyUrl;
  final String appStoreUrl;
  final String logoPath;
  final String primaryColor;
  final String accentColor;

  static FlavorConfig? _instance;

  factory FlavorConfig({
    required Flavor flavor,
    required String name,
    required String appId,
    required String appStoreId,
    required String appName,
    required String baseUrl,
    required String privacyPolicyUrl,
    required String appStoreUrl,
    required String logoPath,
    required String primaryColor,
    required String accentColor,
  }) {
    _instance ??= FlavorConfig._internal(
      flavor: flavor,
      name: name,
      appId: appId,
      appStoreId: appStoreId,
      appName: appName,
      baseUrl: baseUrl,
      privacyPolicyUrl : privacyPolicyUrl,
      appStoreUrl : privacyPolicyUrl,
      logoPath: logoPath,
      primaryColor: primaryColor,
      accentColor: accentColor,


    );
    return _instance!;
  }

  FlavorConfig._internal( {
    required this.flavor,
    required this.name,
    required this.appId,
    required this.appStoreId,
    required this.appName,
    required this.baseUrl,
    required this.privacyPolicyUrl,
    required this.appStoreUrl,
    required this.logoPath,
    required this.primaryColor,
    required this.accentColor,
  });

  static FlavorConfig get instance {
    if (_instance == null) {
      throw Exception("FlavorConfig must be initialized first");
    }
    return _instance!;
  }

  static bool isKoifish() => _instance?.flavor == Flavor.koifish;
  static bool isGoldfish() => _instance?.flavor == Flavor.goldfish;
  static bool isArwanafish() => _instance?.flavor == Flavor.arwanafish;
}
