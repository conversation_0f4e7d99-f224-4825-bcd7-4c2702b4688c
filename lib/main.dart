import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:masterkoi_app/screens/utama_screen.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'utils/theme.dart';
import 'utils/app_localizations_delegate.dart';
import 'providers/rekpen_provider.dart';
import 'providers/auth_provider.dart';
import 'providers/wishlist_provider.dart';
import 'screens/home_screen.dart';
import 'screens/onboarding/onboarding_screen.dart';
import 'screens/language/language_selection_screen.dart';
import 'providers/theme_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

  // Check if user has seen onboarding
  final prefs = await SharedPreferences.getInstance();
  final hasSeenOnboarding = prefs.getBool('hasSeenOnboarding') ?? false;

  // Get initial locale
  final String? languageCode = prefs.getString('languageCode');
  final Locale initialLocale = languageCode != null ? Locale(languageCode) : Locale('');

  runApp(MasterKoiApp(
    initialLocale: initialLocale,
    hasSeenOnboarding: hasSeenOnboarding,
  ));
}

class MasterKoiApp extends StatelessWidget {
  final Locale initialLocale;
  final bool hasSeenOnboarding;

  const MasterKoiApp({
    super.key,
    required this.initialLocale,
    required this.hasSeenOnboarding,
  });

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => ThemeProvider()),
        ChangeNotifierProvider(create: (context) => AuthProvider()),
        ChangeNotifierProvider(create: (context) => WishlistProvider()),
        ChangeNotifierProvider(create: (context) => RekpenProvider()),
      ],
      child: MaterialApp(
        title: 'Master Koi App',
        debugShowCheckedModeBanner: false,
        theme: AppTheme.lightTheme,
        locale: initialLocale,
        supportedLocales: const [
          Locale('id', ''), // Indonesian
          Locale('en', ''), // English
        ],
        localizationsDelegates: const [
          AppLocalizationsDelegate(),
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        home: _getInitialScreen(),
      ),
    );
  }

  Widget _getInitialScreen() {
    // Check if language is already selected
    if (!hasSeenOnboarding) {
      return const OnboardingScreen();
    }

    if (initialLocale.languageCode.isEmpty) {
      return const LanguageSelectionScreen();
    }



    return UtamaScreen();
  }
}
