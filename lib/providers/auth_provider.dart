import 'package:flutter/material.dart';
import 'package:http/http.dart';
import '../services/auth_service.dart';
import '../models/user.dart';

class AuthProvider extends ChangeNotifier {
  final AuthService _authService = AuthService();

  bool _isLoading = false;
  bool _isLoggedIn = false;
  String _errorMessage = '';
  User? _user;

  // Getters
  bool get isLoading => _isLoading;
  bool get isLoggedIn => _isLoggedIn;
  String get errorMessage => _errorMessage;
  User? get user => _user;

  // Getters tambahan
  bool get isSeller => _user?.isSeller ?? false;
  bool get isBuyer => _user?.isBuyer ?? false;

  // Constructor
  AuthProvider() {
    _checkLoginStatus();
  }

  // Fungsi untuk mengecek status login
  Future<void> _checkLoginStatus() async {
    _isLoading = true;
    notifyListeners();

    try {
      _isLoggedIn = await _authService.isLoggedIn();

      if (_isLoggedIn) {
        //_user = await _authService.get();
      }
    } catch (e) {
      _errorMessage = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // HTTP Methods
  Future<Map<String, dynamic>> get(String endpoint) async {
    _isLoading = true;
    _errorMessage = '';
    notifyListeners();

    try {
      final result = await _authService.get(endpoint);
      return result;
    } catch (e) {
      _errorMessage = e.toString();
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<Map<String, dynamic>> post(String endpoint, [Map<String, dynamic>? data]) async {
    _isLoading = true;
    _errorMessage = '';
    notifyListeners();

    try {
      final result = await _authService.post(endpoint, data);
      return result;
    } catch (e) {
      _errorMessage = e.toString();
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Fungsi untuk request OTP
  Future<bool> requestOtp(String phoneNumber) async {
    _isLoading = true;
    _errorMessage = '';
    notifyListeners();

    try {
      final result = await _authService.requestOtp(phoneNumber);

      if (result['success']) {
        return true;
      } else {
        _errorMessage = result['message'];
        return false;
      }
    } catch (e) {
      _errorMessage = e.toString();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Fungsi untuk login dengan OTP
  Future<bool> loginWithOtp(String otp) async {
    _isLoading = true;
    _errorMessage = '';
    notifyListeners();

    try {
      final result = await _authService.loginWithOtp(otp);

      if (result['success']) {
        _isLoggedIn = true;

        // Update user data setelah login berhasil
        if (result['data'] != null) {
          _user = User.fromJson(result['data']);

          // Save FCM token jika ada
          if (_user?.fcmToken != null) {
            await saveFcmToken(_user!.fcmToken!);
          }
        }

        await _checkLoginStatus();
        return true;
      } else {
        _errorMessage = result['message'];
        return false;
      }
    } catch (e) {
      _errorMessage = e.toString();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Fungsi untuk mendapatkan profil pengguna
  Future<bool> getProfile() async {
    _isLoading = true;
    _errorMessage = '';
    notifyListeners();

    try {
      final result = await _authService.getProfile();

      if (result['success']) {
        if (result['data'] != null) {
          _user = User.fromJson(result['data']);
        }
        await _checkLoginStatus();
        return true;
      } else {
        _errorMessage = result['message'];

        // Jika action adalah login, berarti sesi habis
        if (result.containsKey('action') && result['action'] == 'login') {
          await logout();
        }

        return false;
      }
    } catch (e) {
      _errorMessage = e.toString();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Fungsi untuk request PIN (lupa PIN)
  Future<bool> requestPin() async {
    _isLoading = true;
    _errorMessage = '';
    notifyListeners();

    try {
      final result = await _authService.requestPin();

      if (result['success']) {
        return true;
      } else {
        _errorMessage = result['message'];

        // Jika action adalah login, berarti sesi habis
        if (result.containsKey('action') && result['action'] == 'login') {
          await logout();
        }

        return false;
      }
    } catch (e) {
      _errorMessage = e.toString();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Fungsi untuk membuat PIN baru
  Future<bool> createPin(String newPin, String confirmPin) async {
    _isLoading = true;
    _errorMessage = '';
    notifyListeners();

    try {
      final result = await _authService.createOrChangePin(
        newPin: newPin,
        confirmPin: confirmPin,
      );

      if (result['success']) {
        // Update user data
        if (_user != null) {
          //_user = _user!.copyWith(hasPin: true);
        }
        return true;
      } else {
        _errorMessage = result['message'];

        // Jika action adalah login, berarti sesi habis
        if (result.containsKey('action') && result['action'] == 'login') {
          await logout();
        }

        return false;
      }
    } catch (e) {
      _errorMessage = e.toString();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Fungsi untuk mengubah PIN
  Future<bool> changePin(
    String oldPin,
    String newPin,
    String confirmPin,
  ) async {
    _isLoading = true;
    _errorMessage = '';
    notifyListeners();

    try {
      final result = await _authService.createOrChangePin(
        oldPin: oldPin,
        newPin: newPin,
        confirmPin: confirmPin,
      );

      if (result['success']) {
        return true;
      } else {
        _errorMessage = result['message'];

        // Jika action adalah login, berarti sesi habis
        if (result.containsKey('action') && result['action'] == 'login') {
          await logout();
        }

        return false;
      }
    } catch (e) {
      _errorMessage = e.toString();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Fungsi untuk verifikasi PIN
  Future<bool> verifyPin(String pin) async {
    _isLoading = true;
    _errorMessage = '';
    notifyListeners();

    try {
      final result = await _authService.verifyPin(pin);

      if (result['success']) {
        return true;
      } else {
        _errorMessage = result['message'];

        // Jika action adalah login, berarti sesi habis
        if (result.containsKey('action') && result['action'] == 'login') {
          await logout();
        }

        return false;
      }
    } catch (e) {
      _errorMessage = e.toString();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Fungsi untuk menyimpan FCM token
  Future<bool> saveFcmToken(String token) async {
    try {
      final result = await _authService.saveFcmToken(token);

      if (result['success']) {
        // Update user data
        if (_user != null) {
          _user = _user!.copyWith(fcmToken: token);
          notifyListeners();
        }
        return true;
      } else {
        _errorMessage = result['message'];

        // Jika action adalah login, berarti sesi habis
        if (result.containsKey('action') && result['action'] == 'login') {
          await logout();
        }

        return false;
      }
    } catch (e) {
      _errorMessage = e.toString();
      return false;
    }
  }

  // Fungsi untuk logout
  Future<void> logout() async {
    _isLoading = true;
    notifyListeners();

    try {
      await _authService.logout();
      _isLoggedIn = false;
      _user = null;
      _errorMessage = '';
    } catch (e) {
      _errorMessage = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Fungsi untuk menghapus error message
  void clearError() {
    _errorMessage = '';
    notifyListeners();
  }
}
