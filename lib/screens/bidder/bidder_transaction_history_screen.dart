import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../models/ikan_lelang.dart';
import '../../services/api_service.dart';
import '../../utils/colors.dart';
import '../../widgets/interactive_star_rating.dart';
import '../ikan_lelang/ikan_detail_screen.dart';
import '../ikan_lelang/ikan_rating_screen.dart';

/// BidderTransactionHistoryScreen - Complete transaction history
/// Based on MasterKoiBot BidderHistori.swift with enhanced functionality
class BidderTransactionHistoryScreen extends StatefulWidget {
  const BidderTransactionHistoryScreen({Key? key}) : super(key: key);

  @override
  State<BidderTransactionHistoryScreen> createState() => _BidderTransactionHistoryScreenState();
}

class _BidderTransactionHistoryScreenState extends State<BidderTransactionHistoryScreen> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();
  
  bool _isLoading = false;
  String _messageAlert = '';
  List<IkanLelang> _dataItemList = [];
  bool _needLogin = false;
  int _page = 1;
  String _searchText = '';
  
  // Filter options
  String _selectedFilter = 'Semua';
  final List<String> _filterOptions = ['Semua', 'Menang', 'Kalah', 'Belum Selesai'];

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
    _loadTransactionHistory(_searchText);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent) {
      _page++;
      _loadTransactionHistory(_searchText);
    }
  }

  Future<void> _loadTransactionHistory(String query) async {
    if (_page == 1) {
      setState(() {
        _dataItemList = [];
        _isLoading = true;
      });
    }

    try {
      final response = await ApiService().getBidderHistory(
        page: _page,
        search: query,
      );

      setState(() {
        _messageAlert = '';
        for (var item in response) {
          _dataItemList.add(IkanLelang.fromJson(item));
        }
      });
    } catch (e) {
      setState(() => _messageAlert = 'Gagal memuat riwayat: ${e.toString()}');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _refreshData() async {
    _page = 1;
    await _loadTransactionHistory(_searchText);
  }

  void _onSearchChanged(String value) {
    setState(() {
      _searchText = value;
      _page = 1;
    });
    _loadTransactionHistory(_searchText);
  }

  void _onFilterChanged(String? value) {
    if (value != null) {
      setState(() {
        _selectedFilter = value;
        _page = 1;
      });
      _loadTransactionHistory(_searchText);
    }
  }

  List<IkanLelang> get _filteredData {
    if (_selectedFilter == 'Semua') return _dataItemList;
    
    return _dataItemList.where((item) {
      switch (_selectedFilter) {
        case 'Menang':
          return item.statusLelang == 'won' || item.statusLelang == 'menang';
        case 'Kalah':
          return item.statusLelang == 'lost' || item.statusLelang == 'kalah';
        case 'Belum Selesai':
          return item.statusLelang == 'ongoing' || item.statusLelang == 'berlangsung';
        default:
          return true;
      }
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final filteredData = _filteredData;
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Riwayat Transaksi'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshData,
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and Filter Section
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey[50],
            child: Column(
              children: [
                // Search Bar
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Cari ikan...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              _onSearchChanged('');
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                  onChanged: _onSearchChanged,
                ),
                const SizedBox(height: 12),
                
                // Filter Dropdown
                Row(
                  children: [
                    const Text('Filter: ', style: TextStyle(fontWeight: FontWeight.w500)),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedFilter,
                        decoration: InputDecoration(
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide.none,
                          ),
                          filled: true,
                          fillColor: Colors.white,
                          contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        ),
                        items: _filterOptions.map((String value) {
                          return DropdownMenuItem<String>(
                            value: value,
                            child: Text(value),
                          );
                        }).toList(),
                        onChanged: _onFilterChanged,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Content
          Expanded(
            child: RefreshIndicator(
              onRefresh: _refreshData,
              child: _isLoading && _page == 1
                  ? const Center(child: CircularProgressIndicator())
                  : filteredData.isEmpty
                      ? Center(
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.history,
                                  size: 64,
                                  color: Colors.grey[400],
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  _messageAlert.isEmpty
                                      ? 'Belum ada riwayat transaksi'
                                      : _messageAlert,
                                  style: const TextStyle(color: Colors.grey, fontSize: 16),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        )
                      : ListView.builder(
                          controller: _scrollController,
                          padding: const EdgeInsets.all(16),
                          itemCount: filteredData.length + (_isLoading ? 1 : 0),
                          itemBuilder: (context, index) {
                            if (index == filteredData.length) {
                              return const Center(
                                child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator(),
                                ),
                              );
                            }

                            final item = filteredData[index];
                            return _buildTransactionItem(item);
                          },
                        ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionItem(IkanLelang item) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => IkanDetailScreen(idObyekLelang: item.idObyekLelang),
            ),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with status
              Row(
                children: [
                  Expanded(
                    child: Text(
                      item.namaObyekLelang,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  _buildStatusChip(item.statusLelang),
                ],
              ),
              const SizedBox(height: 8),
              
              // Price and bid info
              if (item.viewLastValueBid.isNotEmpty)
                Text(
                  'Harga Akhir: ${item.viewLastValueBid}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.green,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              
              if (item.viewMyLastBid.isNotEmpty)
                Text(
                  'Bid Saya: ${item.viewMyLastBid}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.blue,
                  ),
                ),
              
              const SizedBox(height: 8),
              
              // Seller info
              Text(
                'Seller: ${item.namaMerchantSeller}',
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
              ),
              
              // Date
              if (item.tanggalBerakhir.isNotEmpty)
                Text(
                  'Berakhir: ${_formatDate(item.tanggalBerakhir)}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
              
              const SizedBox(height: 12),
              
              // Action buttons
              _buildActionButtons(item),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color color;
    String text;
    
    switch (status.toLowerCase()) {
      case 'won':
      case 'menang':
        color = Colors.green;
        text = 'Menang';
        break;
      case 'lost':
      case 'kalah':
        color = Colors.red;
        text = 'Kalah';
        break;
      case 'ongoing':
      case 'berlangsung':
        color = Colors.orange;
        text = 'Berlangsung';
        break;
      default:
        color = Colors.grey;
        text = 'Unknown';
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildActionButtons(IkanLelang item) {
    return Row(
      children: [
        if (item.statusLelang.toLowerCase() == 'menang' || item.statusLelang.toLowerCase() == 'won')
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => IkanRatingScreen(
                      idObyekLelang: item.idObyekLelang,
                      halaman: 'Riwayat',
                      judulHalaman: 'Rating Ikan',
                    ),
                  ),
                );
              },
              icon: const Icon(Icons.star, size: 16),
              label: const Text('Beri Rating'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.amber,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 8),
              ),
            ),
          ),
        
        if (item.statusLelang.toLowerCase() == 'menang' || item.statusLelang.toLowerCase() == 'won')
          const SizedBox(width: 8),
        
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => IkanDetailScreen(idObyekLelang: item.idObyekLelang),
                ),
              );
            },
            icon: const Icon(Icons.visibility, size: 16),
            label: const Text('Lihat Detail'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.primary,
              padding: const EdgeInsets.symmetric(vertical: 8),
            ),
          ),
        ),
      ],
    );
  }

  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return DateFormat('dd MMM yyyy, HH:mm').format(date);
    } catch (e) {
      return dateString;
    }
  }
}
