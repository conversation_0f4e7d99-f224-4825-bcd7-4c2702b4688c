import 'package:flutter/material.dart';
import 'package:masterkoi_app/config/flavor_config.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../providers/auth_provider.dart';
import '../../utils/card_view_menu.dart';
import '../../utils/colors.dart';
import '../../utils/constants.dart';
import '../../utils/header_banner.dart';
import '../../widgets/bidder_widgets.dart';
import '../../widgets/carousel_slider_wrapper.dart';
import '../../widgets/menu_card_item.dart';
import '../seller/seller_dashboard_screen.dart';
import '../seller/seller_profile_screen.dart';
import '../seller/seller_lelang_screen.dart';

class SellerFragment extends StatefulWidget {

  @override
  State<SellerFragment> createState() => _SellerFragmentState();
}

class _SellerFragmentState extends State<SellerFragment> {
  bool _isLoading = true;
  String _nameText = "Jual Ikan Anda Disini !";
  String _phoneText = "";
  String _merchantName = "";
  String _logoUrl = "";
  bool _isLoggedIn = false;
  bool _isSeller = false;
  
  List<String> _bannerImages = [];

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    await _loadUserInfo();

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _loadUserInfo() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    
    setState(() {
      _isLoggedIn = authProvider.isLoggedIn;
      _isSeller = authProvider.isSeller;

      if (_isLoggedIn && _isSeller && authProvider.user != null) {
       // _nameText = authProvider.user!.namaUsers;
       // _phoneText = authProvider.user!.noWa;
       // _merchantName = authProvider.user!.namaMerchantSeller;
       // _logoUrl = authProvider.user!.viewUrlFotoLogo;
      } else {
        _nameText = "Jual Ikan Anda Disini !";
        _phoneText = "";
        _merchantName = "";
        _logoUrl = "";
      }
    });
  }
  void _navigateToWebView(String url, String title) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WebViewScreen(url: url, title: title),
      ),
    );
  }

  Future<void> _contactSellerSupport() async {
    const supportNumber = "6282231097260"; // Ganti dengan nomor bantuan yang sebenarnya
    final url = "https://api.whatsapp.com/send/?phone=$supportNumber&text=Saya ingin mendaftar sebagai seller, mohon informasinya.&type=phone_number&app_absent=0";
    final uri = Uri.parse(url);

    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    }
  }


  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Warning text
        Container(
          width: double.infinity,
          color: Colors.red,
          padding: const EdgeInsets.all(8),
          child: const Text(
            "Hati-hati penipuan! Pastikan Anda bertransaksi melalui rekening penjamin resmi.",
            textAlign: TextAlign.center,
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ),

        // Main content
        Expanded(
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                  child: Column(
                    children: [
                      // Banner slider

                      HeaderBanner(
                        imageNumber: 'gambar4.png',
                        text: 'Terintegrasi Real-Time antara WhatsApp dan Telegram, serta terpublis di Official Web 😎',
                        height: 160,
                      ),

                      // Cara Jadi Seller card
                      if (!_isSeller)
                        Transform.translate(
                          offset: const Offset(0, -30),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 8.0),
                            child: Card(
                              margin: const EdgeInsets.only(bottom: 30),
                              elevation: 4,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: InkWell(
                                child: Padding(
                                  padding: const EdgeInsets.all(16.0),
                                  child: Row(
                                    children: [
                                      // New icon
                                      Image.asset(
                                        'assets/images/logo_new.png',
                                        width: 60,
                                        height: 60,
                                      ),

                                      // Click icon
                                      Image.asset(
                                        'assets/images/click.gif',
                                        width: 80,
                                        height: 80,
                                      ),

                                      // Content
                                      Expanded(
                                        child: Column(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            const Icon(
                                              Icons.store,
                                              size: 38,
                                              color: Color(0xFFFFD700), // Gold color
                                            ),
                                            const SizedBox(height: 8),
                                            const Text(
                                              "Cara Jadi Seller",
                                              textAlign: TextAlign.center,
                                              style: TextStyle(
                                                fontSize: 16,
                                                color: Color(0xFF424242), // grey_80
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),

                      // Grid categories
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                        child: Column(
                          children: [
                            // Row 1: Dashboard, Invoice, Point
                            Row(
                              children: [
                                Expanded(
                                  child: CardViewMenuItem(
                                    icon: Icons.assessment_rounded,
                                    text: "Dashboard Seller",
                                    onTap: () => {},
                                  ),
                                ),
                                Expanded(
                                  child: CardViewMenuItem(
                                    icon: Icons.receipt_long,
                                    text: "Invoice Fee BOT",
                                    onTap: () => {},
                                  ),
                                ),
                                Expanded(
                                  child: CardViewMenuItem(
                                    icon: Icons.stars,
                                    text: "Poin",
                                    onTap: () => {},
                                  ),
                                ),
                              ],
                            ),

                            // Row 2: Lelang, Edit Ikan, Ikan Terjual
                            Row(
                              children: [
                                Expanded(
                                  child: CardViewMenuItem(
                                    icon: Icons.play_circle,
                                    text: "Lelang",
                                    onTap: () => {},
                                  ),
                                ),
                                Expanded(
                                  child: CardViewMenuItem(
                                    icon: Icons.edit,
                                    text: "Edit Ikan",
                                    onTap: () => {},
                                  ),
                                ),
                                Expanded(
                                  child: CardViewMenuItem(
                                    icon: Icons.shopping_cart,
                                    text: "Ikan Terjual",
                                    onTap: () => {},
                                  ),
                                ),
                              ],
                            ),

                            // Row 3: Member Aktif, Most Auction, Edit Profil
                            Row(
                              children: [
                                Expanded(
                                  child: CardViewMenuItem(
                                    icon: Icons.person_add,
                                    text: "Member Aktif",
                                    onTap: () => {},
                                  ),
                                ),
                                Expanded(
                                  child: CardViewMenuItem(
                                    icon: Icons.monetization_on,
                                    text: "Most Auction",
                                    onTap: () => {},
                                  ),
                                ),
                                Expanded(
                                  child: CardViewMenuItem(
                                    icon: Icons.manage_accounts,
                                    text: "Edit Profil Seller",
                                    onTap: () => {},
                                  ),
                                ),
                              ],
                            ),

                            // Row 4: Ads Analytics, Rekening Penjamin, Bad Debt
                            Row(
                              children: [
                                Expanded(
                                  child: CardViewMenuItem(
                                    icon: Icons.desktop_mac,
                                    text: "Ads Analytic",
                                    onTap: () => {},
                                  ),
                                ),
                                Expanded(
                                  child: CardViewMenuItem(
                                    icon: Icons.credit_card,
                                    text: "Rekening Penjamin",
                                    onTap: () => {},
                                  ),
                                ),
                                Expanded(
                                  child: CardViewMenuItem(
                                    icon: Icons.store_mall_directory_outlined,
                                    text: "Seller Bad Debt",
                                    onTap: () => {},
                                  ),
                                ),
                              ],
                            ),

                            const SizedBox(height: 24),
                          ],
                        ),
                      ),

                      const SizedBox(height: 24),
                    ],
                  ),
                ),
        ),
      ],
    );
  }
}
