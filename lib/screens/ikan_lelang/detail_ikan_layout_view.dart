import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../models/ikan_lelang.dart';

/// DetailIkanLayoutView - Single file approach
/// Converted from Swift DetailIkanLayoutView.swift
/// Expandable section showing detailed fish information
class DetailIkanLayoutView extends StatefulWidget {
  final IkanLelang ikanLelang;

  const DetailIkanLayoutView({super.key, required this.ikanLelang});

  @override
  State<DetailIkanLayoutView> createState() => _DetailIkanLayoutViewState();
}

class _DetailIkanLayoutViewState extends State<DetailIkanLayoutView> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            InkWell(
              onTap: () {
                setState(() {
                  _isExpanded = !_isExpanded;
                });
              },
              child: Row(
                children: [
                  const Icon(Icons.info_outline),
                  const SizedBox(width: 8),
                  const Text(
                    'Detail Ikan',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const Spacer(),
                  Icon(
                    _isExpanded
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                  ),
                ],
              ),
            ),

            // Expandable Content
            if (_isExpanded) ...[
              const SizedBox(height: 16),

              // Auction Details (only for non-deal items)
              if (widget.ikanLelang.opsiDeal == 0) ...[
                // Poster Image
                _buildDetailItem('Poster', null, widget: _buildPosterImage()),

                // Auction End Time
                _buildDetailItem(
                  widget.ikanLelang.opsiDeal == 1 ? 'Close' : 'Batas Lelang',
                  widget.ikanLelang.closeBidTimer,
                ),

                // Views
                _buildDetailItem('View', widget.ikanLelang.dilihat),

                // Auction Type
                _buildDetailItem(
                  'Jenis Lelang',
                  widget.ikanLelang.kodeLelangMode,
                ),

                // Buy It Now (if available)
                if (widget.ikanLelang.bin.isNotEmpty)
                  _buildDetailItem('Buy It Now (BIN)', widget.ikanLelang.bin),

                // Open Bid
                _buildDetailItem('Open Bid', widget.ikanLelang.ob),

                // Bid Increment
                _buildDetailItem('Kelipatan Bid', widget.ikanLelang.kb),

                // Total Bids
                _buildDetailItem(
                  'Jumlah Bid',
                  '${widget.ikanLelang.jmlBid} xBid',
                ),

                // Last Bid Value
                _buildDetailItem(
                  'Nilai Bid terakhir',
                  widget.ikanLelang.viewLastValueBid,
                ),
              ],

              // Gender
              _buildDetailItem(
                'Gender',
                _getGenderText(widget.ikanLelang.gender),
              ),

              // Description
              _buildDetailItem(
                'Keterangan',
                widget.ikanLelang.keterangan.length > 2
                    ? widget.ikanLelang.keterangan
                    : '-',
              ),

              // Certificate (if available)
              if (widget
                  .ikanLelang
                  .viewFoto
                  .isNotEmpty) // Using viewFoto as certificate placeholder
                _buildDetailItem(
                  'Sertifikat',
                  null,
                  widget: _buildCertificateImage(),
                ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDetailItem(String label, String? value, {Widget? widget}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(label, style: const TextStyle(color: Colors.grey, fontSize: 14)),
          const SizedBox(height: 4),
          if (widget != null)
            Padding(padding: const EdgeInsets.only(left: 16), child: widget)
          else
            Padding(
              padding: const EdgeInsets.only(left: 16),
              child: Text(value ?? '-', style: const TextStyle(fontSize: 16)),
            ),
          const Divider(),
        ],
      ),
    );
  }

  Widget _buildPosterImage() {
    return Container(
      height: 200,
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child:
            widget.ikanLelang.viewFoto.isNotEmpty
                ? CachedNetworkImage(
                  imageUrl: widget.ikanLelang.viewFoto,
                  fit: BoxFit.cover,
                  placeholder:
                      (context, url) =>
                          const Center(child: CircularProgressIndicator()),
                  errorWidget:
                      (context, url, error) => const Center(
                        child: Icon(Icons.photo, size: 50, color: Colors.grey),
                      ),
                )
                : const Center(
                  child: Icon(Icons.photo, size: 50, color: Colors.grey),
                ),
      ),
    );
  }

  Widget _buildCertificateImage() {
    return GestureDetector(
      onTap: () {
        _showImageDialog(widget.ikanLelang.viewFoto);
      },
      child: Container(
        height: 200,
        width: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child:
              widget.ikanLelang.viewFoto.isNotEmpty
                  ? CachedNetworkImage(
                    imageUrl: widget.ikanLelang.viewFoto,
                    fit: BoxFit.cover,
                    placeholder:
                        (context, url) =>
                            const Center(child: CircularProgressIndicator()),
                    errorWidget:
                        (context, url, error) => const Center(
                          child: Icon(
                            Icons.photo,
                            size: 50,
                            color: Colors.grey,
                          ),
                        ),
                  )
                  : const Center(
                    child: Icon(Icons.photo, size: 50, color: Colors.grey),
                  ),
        ),
      ),
    );
  }

  String _getGenderText(String gender) {
    switch (gender.toUpperCase()) {
      case 'F':
        return 'Female';
      case 'M':
        return 'Male';
      case 'U':
        return 'Uncheck';
      default:
        return 'Uncheck';
    }
  }

  void _showImageDialog(String imageUrl) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          child: Stack(
            children: [
              Center(
                child: Container(
                  constraints: BoxConstraints(
                    maxHeight: MediaQuery.of(context).size.height * 0.8,
                    maxWidth: MediaQuery.of(context).size.width * 0.9,
                  ),
                  child: CachedNetworkImage(
                    imageUrl: imageUrl,
                    fit: BoxFit.contain,
                    placeholder:
                        (context, url) => const Center(
                          child: CircularProgressIndicator(color: Colors.white),
                        ),
                    errorWidget:
                        (context, url, error) => const Center(
                          child: Icon(
                            Icons.error,
                            color: Colors.white,
                            size: 50,
                          ),
                        ),
                  ),
                ),
              ),
              Positioned(
                top: 40,
                right: 20,
                child: IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close, color: Colors.white, size: 30),
                  style: IconButton.styleFrom(
                    backgroundColor: Colors.black54,
                    shape: const CircleBorder(),
                  ),
                ),
              ),
              // Stats overlay at bottom
              Positioned(
                bottom: 20,
                left: 0,
                right: 0,
                child: Container(
                  padding: const EdgeInsets.all(16),
                  color: Colors.black54,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _buildStatItem(
                        Icons.visibility,
                        widget.ikanLelang.dilihat,
                      ),
                      const SizedBox(width: 16),
                      _buildStatItem(
                        Icons.favorite,
                        widget.ikanLelang.difavoritkan,
                      ),
                      const SizedBox(width: 16),
                      _buildStatItem(Icons.gavel, widget.ikanLelang.jmlBid),
                      if (widget.ikanLelang.ikanKc >= 1) ...[
                        const SizedBox(width: 16),
                        _buildStatItem(
                          Icons.note,
                          widget.ikanLelang.kodeLelangMode,
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatItem(IconData icon, String value) {
    return Row(
      children: [
        Icon(icon, color: Colors.white, size: 16),
        const SizedBox(width: 4),
        Text(value, style: const TextStyle(color: Colors.white, fontSize: 12)),
      ],
    );
  }
}
