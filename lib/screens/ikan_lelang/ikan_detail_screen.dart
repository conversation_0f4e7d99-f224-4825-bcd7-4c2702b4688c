import 'package:flutter/material.dart';
import 'package:masterkoi_app/config/flavor_config.dart';
import 'package:masterkoi_app/screens/auth/otp_verification_screen.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:intl/intl.dart';
import '../../models/ikan_lelang.dart';
import '../../providers/auth_provider.dart';
import '../../services/ikan_lelang_service.dart';
import '../../utils/colors.dart';
import '../../utils/constants.dart';
import '../../widgets/carousel_slider_wrapper.dart';
import '../../widgets/html_content.dart';
import '../auth/pin_verification_screen.dart';
import '../../widgets/progress_bar.dart';
import '../../widgets/related_fish_listings.dart';

class IkanDetailScreen extends StatefulWidget {
  final String idObyekLelang;
  final String title;
  
  const IkanDetailScreen({
    Key? key,
    required this.idObyekLelang,
    this.title = "Detail Ikan",
  }) : super(key: key);

  @override
  State<IkanDetailScreen> createState() => _IkanDetailScreenState();
}

class _IkanDetailScreenState extends State<IkanDetailScreen> {
  final TextEditingController _bidController = TextEditingController();

  bool _isLoading = true;
  bool _isFavorite = false;
  bool _isLoadingBid = false;
  bool _confirmAlert = false;
  bool _showingAlert = false;
  String _messageAlert = '';
  int _kodeAlert = 0;
  IkanLelang? _ikan;
  String _errorMessage = '';
  List<String> _imageList = [];
  String _historiBidder = '';
  Timer? _countdownTimer;
  Duration _timeRemaining = Duration.zero;
  bool _isCountingDown = false;
  String _pin = '';

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _bidController.dispose();
    _countdownTimer?.cancel();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final ikanLelangService = IkanLelangService();
      final ikan = await ikanLelangService.getIkanLelangDetail(
          widget.idObyekLelang);

      if (ikan != null) {
        setState(() {
          _ikan = ikan;
          _isFavorite = ikan.difavoritkan == '1';

          // Prepare image list
          _imageList = [];
          if (ikan.viewFoto.isNotEmpty) {
            _imageList.add(ikan.viewFoto);
          }
          
          // Start countdown if needed
          _startCountdown();

          _isLoading = false;
        });
        
        // Load related data
        if (widget.title == "Ikan Terjual") {
          _getBidderHistori();
        }
      } else {
        setState(() {
          _errorMessage = 'Gagal memuat data ikan';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error: ${e.toString()}';
        _isLoading = false;
      });
    }
  }
  
  void _startCountdown() {
    if (_ikan == null) return;
    
    _countdownTimer?.cancel();
    
    final dateFormat = DateFormat("yyyy-MM-dd HH:mm:ss");
    final targetDate = dateFormat.parse(_ikan!.lastCloseTimer);
    final now = DateTime.now();
    final difference = targetDate.difference(now);
    final twoHours = const Duration(hours: 2);
    
    if (difference > Duration.zero && difference <= twoHours) {
      setState(() {
        _isCountingDown = true;
        _timeRemaining = difference;
      });
      
      _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        if (_timeRemaining > Duration.zero) {
          setState(() {
            _timeRemaining = _timeRemaining - const Duration(seconds: 1);
          });
        } else {
          _stopCountdown();
        }
      });
    }
  }
  
  void _stopCountdown() {
    _countdownTimer?.cancel();
    _countdownTimer = null;
    setState(() {
      _isCountingDown = false;
    });
  }
  
  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);
    return "${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}";
  }

  Future<void> _getBidderHistori() async {
    if (_ikan == null) return;
    
    try {
      final ikanLelangService = IkanLelangService();
      final histori = await ikanLelangService.getBidderHistori(_ikan!.idBuyer);
      
      if (histori.isNotEmpty) {
        setState(() {
          _historiBidder = histori;
        });
      }
    } catch (e) {
      print('Error getting bidder history: $e');
    }
  }

  Future<void> _toggleFavorite() async {
    if (_ikan == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final ikanLelangService = IkanLelangService();
      final result = _isFavorite
          ? await ikanLelangService.removeFromWishlist(widget.idObyekLelang)
          : await ikanLelangService.addToWishlist(widget.idObyekLelang);

      if (result['success']) {
        setState(() {
          _isFavorite = !_isFavorite;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_isFavorite
                ? 'Berhasil menambahkan ke wishlist'
                : 'Berhasil menghapus dari wishlist'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(result['message']),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showBidDialog() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    if (!authProvider.isLoggedIn) {
      _showLoginDialog();
      return;
    }

    if (!authProvider.isBuyer) {
      _showBukanBuyerDialog();
      return;
    }

    if (_ikan == null) return;

    // Set initial bid value
    int initialBid = 0;
    if (_ikan!.lastValueBid > 0) {
      initialBid =
          _ikan!.lastValueBid + int.parse(_ikan!.nextKb.replaceAll(',', ''));
    } else {
      initialBid = _ikan!.valueOb;
    }

    _bidController.text = initialBid.toString();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(_ikan!.opsiDeal == 1 ? 'Beli Sekarang' : 'Bid Ikan'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _bidController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                labelText: _ikan!.opsiDeal == 1 ? 'Harga Beli' : 'Nilai Bid',
                border: const OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              _ikan!.opsiDeal == 1
                  ? 'Apakah Anda yakin membeli ikan ${_ikan!.namaObyekLelang} senilai ${_ikan!.viewNextKb}?'
                  : 'Apakah Anda yakin melakukan BID ikan ${_ikan!.namaObyekLelang} senilai ${_ikan!.viewNextKb}?',
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 8),
            Text(
              'Saya bersedia dijadikan pelaku BNR, jika membatalkan pembelian ini.\n\n'
              'Harga yang dimenangkan belum termasuk Ongkos Kirim & Packing.\n\n'
              '${FlavorConfig.instance.appName} hanya menjamin transaksi yang menggunakan rekpen15k.',
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Batal'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _processBid();
            },
            child: Text(_ikan!.opsiDeal == 1 ? 'Beli Sekarang' : 'Bid Sekarang'),
          ),
        ],
      ),
    );
  }

  void _processBid() {
    if (_bidController.text.isEmpty) return;

    final int bidValue = int.tryParse(_bidController.text) ?? 0;
    if (bidValue <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Nilai bid tidak valid'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Navigate to PIN verification
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            PinVerificationScreen(
              onPinVerified: (pin) => _submitBid(bidValue, pin),
            ),
      ),
    );
  }

  Future<void> _submitBid(int bidValue, String pin) async {
    setState(() {
      _isLoadingBid = true;
    });

    try {
      final ikanLelangService = IkanLelangService();
      final command = _ikan!.opsiDeal == 1 ? "bin" : _ikan!.nextKb;
      
      final result = await ikanLelangService.bidIkan(
        idObyekLelang: widget.idObyekLelang,
        command: command,
        pin: pin,
      );

      setState(() {
        _showingAlert = true;
        _messageAlert = result['meta']['message'] ?? 'Unknown error';
        _kodeAlert = result['meta']['code'] ?? 0;
      });

      if (result['meta']['code'] == 200) {
        // Reload data to get updated bid info
        await _loadData();
      }
    } catch (e) {
      setState(() {
        _showingAlert = true;
        _messageAlert = 'Error: ${e.toString()}';
        _kodeAlert = 500;
      });
    } finally {
      setState(() {
        _isLoadingBid = false;
        _confirmAlert = false;
      });
    }
  }

  Future<void> _contactSeller() async {
    if (_ikan == null) return;

    final phoneNumber = _ikan!.noHpSeller;
    if (phoneNumber.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Nomor telepon seller tidak tersedia'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final message = 'Halo, saya tertarik dengan ikan ${_ikan!.namaObyekLelang} yang dilelang di ${FlavorConfig.instance.appName}';
    final url = "https://api.whatsapp.com/send/?phone=$phoneNumber&text=${Uri.encodeComponent(message)}&type=phone_number&app_absent=0";
    final uri = Uri.parse(url);

    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Tidak dapat membuka WhatsApp'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showLoginDialog() {
    showDialog(
      context: context,
      builder: (context) =>
          AlertDialog(
            title: const Text('Login Diperlukan'),
            content: const Text('Anda perlu login untuk mengakses fitur ini.'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Batal'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const OtpVerificationScreen(),
                    ),
                  );
                },
                child: const Text('Login'),
              ),
            ],
          ),
    );
  }

  void _showBukanBuyerDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Akses Terbatas'),
        content: const Text(
            'Fitur ini hanya tersedia untuk akun buyer. Silakan hubungi admin untuk mengubah status akun Anda.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _shareIkan() {
    if (_ikan == null) return;
    
    final textToShare = '${_ikan!.namaObyekLelang}\n'
        '${_ikan!.shortLinkObyekLelang}\n\n'
        'Download app ${FlavorConfig.instance.appName} di:\n'
        '${FlavorConfig.instance.baseUrl}/app';
    
    Share.share(textToShare);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: Icon(_isFavorite ? Icons.favorite : Icons.favorite_border),
            color: _isFavorite ? Colors.red : Colors.white,
            onPressed: _toggleFavorite,
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: _shareIkan,
          ),
        ],
      ),
      body: _isLoading 
        ? Center(child: ProgressBar())
        : _ikan == null
          ? Center(child: Text(_errorMessage.isEmpty ? 'Data tidak ditemukan' : _errorMessage))
          : Stack(
              children: [
                // Main content
                SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Fish detail view
                      _buildIkanDetailView(),
                      
                      // Bidder history if available
                      if (_historiBidder.isNotEmpty) 
                        _buildHistoryWinnerLayout(),
                      
                      const Divider(),
                      
                      // Seller info
                      _buildSellerLayout(),
                      
                      const Divider(),
                      
                      // Fish details
                      _buildDetailIkanLayout(),
                      
                      // Auction history for regular auction
                      if (_ikan!.opsiDeal == 0) 
                        _buildHistoriLelangLayout(),
                      
                      // Related fish listings
                      RelatedFishListingsView(
                        title: "Ikan di seller ini",
                        endpoint: "ikan/${_ikan!.idObyekLelang}/ikan-lainnya",
                        idObyekLelang: _ikan!.idObyekLelang,
                        idLelang: _ikan!.idLelang,
                      ),
                      
                      RelatedFishListingsView(
                        title: "Ikan sejenis",
                        endpoint: "ikan/${_ikan!.idObyekLelang}/ikan-lain-sejenis",
                        idObyekLelang: _ikan!.idObyekLelang,
                      ),
                      
                      RelatedFishListingsView(
                        title: "Ikan pada Priority Seller",
                        endpoint: "ikan/priority",
                        idObyekLelang: _ikan!.idObyekLelang,
                      ),
                      
                      const SizedBox(height: 80), // Space for bottom button
                    ],
                  ),
                ),
                
                // Bid/Buy button
                if (_ikan!.visibilityBtnBid == 1)
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: _confirmAlert
                      ? _buildConfirmButtons()
                      : _buildBidButton(),
                  ),
                
                // Alert overlay
                if (_showingAlert)
                  _buildAlertOverlay(),
              ],
            ),
    );
  }
  
  Widget _buildIkanDetailView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Image carousel
        CarouselSliderWrapper(
          imageUrls: _imageList,
          height: 250,
        ),
        
        // Fish name and status
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      _ikan!.namaObyekLelang,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  if (_isCountingDown)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(Icons.timer, size: 16, color: Colors.white),
                          const SizedBox(width: 4),
                          Text(
                            _formatDuration(_timeRemaining),
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
              
              const SizedBox(height: 8),
              
              // Fish status
              if (_ikan!.viewStatusBid.isNotEmpty)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Color(int.parse(_ikan!.colorStatusBid.replaceAll('#', '0xFF'))),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    _ikan!.viewStatusBid,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              
              const SizedBox(height: 16),
              
              // Bid information
              if (_ikan!.opsiDeal == 0)
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('OB', style: TextStyle(fontSize: 12)),
                        Text(
                          _ikan
