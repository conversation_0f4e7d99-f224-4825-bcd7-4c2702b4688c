import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';
import 'dart:async';
import '../../models/ikan_lelang.dart';

/// IkanDetailView - Single file approach
/// Converted from Swift IkanDetailView.swift with enhanced functionality
/// Includes image carousel, video player, countdown timer, and basic fish info
class IkanDetailView extends StatefulWidget {
  final IkanLelang ikanLelang;

  const IkanDetailView({super.key, required this.ikanLelang});

  @override
  State<IkanDetailView> createState() => _IkanDetailViewState();
}

class _IkanDetailViewState extends State<IkanDetailView> {
  final PageController _pageController = PageController();
  VideoPlayerController? _videoController;
  ChewieController? _chewieController;
  Timer? _countdownTimer;

  Duration _timeRemaining = Duration.zero;
  bool _isCountingDown = false;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _initializeVideo();
    _startCountdown();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _videoController?.dispose();
    _chewieController?.dispose();
    _countdownTimer?.cancel();
    super.dispose();
  }

  void _initializeVideo() {
    if (widget.ikanLelang.viewVideo.isNotEmpty) {
      _videoController = VideoPlayerController.networkUrl(
        Uri.parse(widget.ikanLelang.viewVideo),
      );
      _videoController!.initialize().then((_) {
        _chewieController = ChewieController(
          videoPlayerController: _videoController!,
          autoPlay: false,
          looping: false,
          showControls: true,
          aspectRatio: _videoController!.value.aspectRatio,
        );
        if (mounted) setState(() {});
      });
    }
  }

  void _startCountdown() {
    if (widget.ikanLelang.closeBidTimer.isEmpty) return;

    try {
      final targetDate = DateTime.parse(widget.ikanLelang.closeBidTimer);
      final currentTime = DateTime.now();
      final timeDifference = targetDate.difference(currentTime);
      const twoHours = Duration(hours: 2);

      if (timeDifference.isNegative || timeDifference > twoHours) return;

      setState(() {
        _isCountingDown = true;
        _timeRemaining = timeDifference;
      });

      _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        if (_timeRemaining.inSeconds > 1) {
          setState(() {
            _timeRemaining = _timeRemaining - const Duration(seconds: 1);
          });
        } else {
          _stopCountdown();
        }
      });
    } catch (e) {
      // Invalid date format, ignore countdown
    }
  }

  void _stopCountdown() {
    _countdownTimer?.cancel();
    setState(() => _isCountingDown = false);
  }

  String _formatTime(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    final seconds = duration.inSeconds % 60;
    return '${hours.toString().padLeft(2, '0')}:'
        '${minutes.toString().padLeft(2, '0')}:'
        '${seconds.toString().padLeft(2, '0')}';
  }

  void _copyLink() {
    Clipboard.setData(
      ClipboardData(text: widget.ikanLelang.shortLinkObyekLelang),
    );
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Link ikan berhasil disalin.')),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Image/Video Carousel Section
        SizedBox(
          height: 260,
          child: Stack(
            children: [
              PageView(
                controller: _pageController,
                onPageChanged: (index) => setState(() => _currentIndex = index),
                children: [
                  // Main Image
                  _buildImageWidget(widget.ikanLelang.viewFoto),
                  // Video
                  _buildVideoWidget(),
                  // Poster Image
                  _buildImageWidget(
                    widget.ikanLelang.viewFoto,
                  ), // Using viewFoto as fallback
                ],
              ),

              // KC Badge
              if (widget.ikanLelang.ikanKc >= 1)
                Positioned(
                  bottom: 8,
                  left: 8,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      widget.ikanLelang.kodeLelangMode,
                      style: const TextStyle(
                        color: Colors.white,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
                ),

              // Countdown Timer
              if (_isCountingDown)
                Positioned(
                  bottom: 8,
                  right: 8,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      _formatTime(_timeRemaining),
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),

              // Location Badge
              if (widget.ikanLelang.lokasiIkan.isNotEmpty)
                Positioned(
                  bottom: _isCountingDown ? 40 : 8,
                  right: 8,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.8),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      widget.ikanLelang.lokasiIkan,
                      style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),

              // Navigation Arrows
              Positioned(
                left: 16,
                top: 0,
                bottom: 0,
                child: Center(
                  child: IconButton(
                    onPressed: () {
                      final newIndex = (_currentIndex - 1 + 3) % 3;
                      _pageController.animateToPage(
                        newIndex,
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                    },
                    icon: const Icon(
                      Icons.chevron_left,
                      color: Colors.white,
                      size: 40,
                    ),
                  ),
                ),
              ),
              Positioned(
                right: 16,
                top: 0,
                bottom: 0,
                child: Center(
                  child: IconButton(
                    onPressed: () {
                      final newIndex = (_currentIndex + 1) % 3;
                      _pageController.animateToPage(
                        newIndex,
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                    },
                    icon: const Icon(
                      Icons.chevron_right,
                      color: Colors.white,
                      size: 40,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),

        // Page Indicator and Stats
        Container(
          color: Theme.of(context).primaryColor,
          padding: const EdgeInsets.all(8),
          child: Column(
            children: [
              // Custom Page Indicator
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(3, (index) {
                  return Container(
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color:
                          _currentIndex == index
                              ? Colors.white
                              : Colors.white.withValues(alpha: 0.5),
                    ),
                  );
                }),
              ),
              const SizedBox(height: 8),

              // Stats Row
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildStatItem(Icons.visibility, widget.ikanLelang.dilihat),
                  const SizedBox(width: 16),
                  _buildStatItem(
                    Icons.favorite,
                    widget.ikanLelang.difavoritkan,
                  ),
                  const SizedBox(width: 16),
                  _buildStatItem(Icons.gavel, widget.ikanLelang.jmlBid),
                  if (widget.ikanLelang.ikanKc >= 1) ...[
                    const SizedBox(width: 16),
                    _buildStatItem(
                      Icons.note,
                      widget.ikanLelang.kodeLelangMode,
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),

        // Fish Info Section
        Container(
          color: Colors.grey[800],
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Fish Name
              Text(
                widget.ikanLelang.namaObyekLelang,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),

              // Copy Link
              GestureDetector(
                onTap: _copyLink,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      widget.ikanLelang.shortLinkObyekLelang,
                      style: const TextStyle(color: Colors.white),
                    ),
                    const SizedBox(width: 8),
                    const Icon(Icons.copy, color: Colors.blue, size: 20),
                    const Text('Copy', style: TextStyle(color: Colors.blue)),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              // Buy It Now or Bid Info
              if (widget.ikanLelang.opsiDeal == 1)
                const Text(
                  'Buy It Now',
                  style: TextStyle(
                    color: Colors.green,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                )
              else
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // OB
                    Column(
                      children: [
                        Text(
                          widget.ikanLelang.ob,
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const Text(
                          'OB',
                          style: TextStyle(color: Colors.white, fontSize: 12),
                        ),
                      ],
                    ),

                    // Last Bid
                    Column(
                      children: [
                        Text(
                          widget.ikanLelang.viewLastValueBid.isNotEmpty
                              ? widget.ikanLelang.viewLastValueBid
                              : 'No BID',
                          style: TextStyle(
                            color:
                                widget.ikanLelang.colorLastValueBid.isNotEmpty
                                    ? _getColorFromString(
                                      widget.ikanLelang.colorLastValueBid,
                                    )
                                    : Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const Text(
                          'Last BID',
                          style: TextStyle(color: Colors.white, fontSize: 12),
                        ),
                      ],
                    ),

                    // KB
                    Column(
                      children: [
                        Text(
                          widget.ikanLelang.kb,
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const Text(
                          'KB',
                          style: TextStyle(color: Colors.white, fontSize: 12),
                        ),
                      ],
                    ),
                  ],
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildImageWidget(String imageUrl) {
    return Container(
      color: Theme.of(context).primaryColor,
      child:
          imageUrl.isNotEmpty
              ? CachedNetworkImage(
                imageUrl: imageUrl,
                fit: BoxFit.contain,
                placeholder:
                    (context, url) => const Center(
                      child: CircularProgressIndicator(color: Colors.white),
                    ),
                errorWidget:
                    (context, url, error) => const Center(
                      child: Icon(Icons.photo, color: Colors.white, size: 50),
                    ),
              )
              : const Center(
                child: Icon(Icons.photo, color: Colors.white, size: 50),
              ),
    );
  }

  Widget _buildVideoWidget() {
    return Container(
      color: Theme.of(context).primaryColor,
      child:
          _chewieController != null
              ? Chewie(controller: _chewieController!)
              : const Center(
                child: Icon(Icons.play_circle, color: Colors.white, size: 50),
              ),
    );
  }

  Widget _buildStatItem(IconData icon, String value) {
    return Row(
      children: [
        Icon(icon, color: Colors.white, size: 16),
        const SizedBox(width: 4),
        Text(value, style: const TextStyle(color: Colors.white, fontSize: 12)),
      ],
    );
  }

  Color _getColorFromString(String colorString) {
    switch (colorString.toLowerCase()) {
      case 'red':
        return Colors.red;
      case 'green':
        return Colors.green;
      case 'blue':
        return Colors.blue;
      case 'orange':
        return Colors.orange;
      default:
        return Colors.white;
    }
  }
}
