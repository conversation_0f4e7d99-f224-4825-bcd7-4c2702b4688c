import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:provider/provider.dart';
import '../../models/ikan_lelang.dart';
import '../../providers/wishlist_provider.dart';

/// IkanLelangLainGridItem - Single file approach
/// Converted from Swift IkanLelangLainGridItem.swift
/// Grid item widget for related fish listings
class IkanLelangLainGridItem extends StatefulWidget {
  final IkanLelang item;
  final VoidCallback? onTap;

  const IkanLelangLainGridItem({super.key, required this.item, this.onTap});

  @override
  State<IkanLelangLainGridItem> createState() => _IkanLelangLainGridItemState();
}

class _IkanLelangLainGridItemState extends State<IkanLelangLainGridItem> {
  @override
  Widget build(BuildContext context) {
    return Consumer<WishlistProvider>(
      builder: (context, wishlistProvider, child) {
        final isBookmarked = wishlistProvider.isBookmarked(
          widget.item.idObyekLelang,
        );

        return SizedBox(
          width: 160, // Fixed width for horizontal scrolling
          child: Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            child: InkWell(
              onTap: widget.onTap,
              borderRadius: BorderRadius.circular(8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Image Section
                  Expanded(
                    flex: 3,
                    child: Stack(
                      children: [
                        // Main Image
                        Container(
                          width: double.infinity,
                          decoration: const BoxDecoration(
                            borderRadius: BorderRadius.vertical(
                              top: Radius.circular(8),
                            ),
                          ),
                          child: ClipRRect(
                            borderRadius: const BorderRadius.vertical(
                              top: Radius.circular(8),
                            ),
                            child:
                                widget.item.viewFoto.isNotEmpty
                                    ? CachedNetworkImage(
                                      imageUrl: widget.item.viewFoto,
                                      fit: BoxFit.cover,
                                      placeholder:
                                          (context, url) => Container(
                                            color: Colors.grey[300],
                                            child: const Center(
                                              child:
                                                  CircularProgressIndicator(),
                                            ),
                                          ),
                                      errorWidget:
                                          (context, url, error) => Container(
                                            color: Colors.grey[300],
                                            child: const Icon(
                                              Icons.error,
                                              color: Colors.red,
                                            ),
                                          ),
                                    )
                                    : Container(
                                      color: Colors.grey[300],
                                      child: const Icon(
                                        Icons.image,
                                        color: Colors.grey,
                                      ),
                                    ),
                          ),
                        ),

                        // KC Badge
                        if (widget.item.ikanKc >= 1)
                          Positioned(
                            top: 8,
                            left: 8,
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: Theme.of(context).primaryColor,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                widget.item.kodeLelangMode,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontStyle: FontStyle.italic,
                                ),
                              ),
                            ),
                          ),

                        // Bookmark Button
                        Positioned(
                          top: 8,
                          right: 8,
                          child: GestureDetector(
                            onTap: () {
                              if (isBookmarked) {
                                wishlistProvider.removeFromWishlist(
                                  widget.item.idObyekLelang,
                                );
                              } else {
                                wishlistProvider.addToWishlist(
                                  widget.item.idObyekLelang,
                                );
                              }
                            },
                            child: Container(
                              padding: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.8),
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                isBookmarked
                                    ? Icons.favorite
                                    : Icons.favorite_border,
                                color: isBookmarked ? Colors.red : Colors.grey,
                                size: 16,
                              ),
                            ),
                          ),
                        ),

                        // Location Badge
                        if (widget.item.lokasiIkan.isNotEmpty)
                          Positioned(
                            bottom: 8,
                            left: 8,
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.8),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                widget.item.lokasiIkan,
                                style: TextStyle(
                                  color: Theme.of(context).primaryColor,
                                  fontSize: 8,
                                  fontWeight: FontWeight.bold,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),

                  // Content Section
                  Expanded(
                    flex: 2,
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Title
                          Text(
                            widget.item.namaObyekLelang,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 11,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),

                          // Price Info
                          if (widget.item.opsiDeal == 1)
                            // Buy It Now
                            Text(
                              'Buy It Now',
                              style: TextStyle(
                                color: Colors.green[600],
                                fontSize: 10,
                                fontWeight: FontWeight.w600,
                              ),
                            )
                          else
                            // Bid Info
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (widget.item.viewLastValueBid.isNotEmpty)
                                  Text(
                                    'Bid: ${widget.item.viewLastValueBid}',
                                    style: TextStyle(
                                      color:
                                          widget
                                                  .item
                                                  .colorLastValueBid
                                                  .isNotEmpty
                                              ? _getColorFromString(
                                                widget.item.colorLastValueBid,
                                              )
                                              : Colors.green,
                                      fontSize: 9,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                Text(
                                  'OB: ${widget.item.ob}',
                                  style: const TextStyle(
                                    color: Colors.grey,
                                    fontSize: 9,
                                  ),
                                ),
                              ],
                            ),

                          const Spacer(),

                          // Stats Row
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              _buildStatItem(
                                Icons.visibility,
                                widget.item.dilihat,
                              ),
                              _buildStatItem(
                                Icons.favorite,
                                widget.item.difavoritkan,
                              ),
                              _buildStatItem(Icons.gavel, widget.item.jmlBid),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatItem(IconData icon, String value) {
    return Row(
      children: [
        Icon(icon, size: 10, color: Colors.grey),
        const SizedBox(width: 2),
        Text(value, style: const TextStyle(fontSize: 8, color: Colors.grey)),
      ],
    );
  }

  Color _getColorFromString(String colorString) {
    switch (colorString.toLowerCase()) {
      case 'red':
        return Colors.red;
      case 'green':
        return Colors.green;
      case 'blue':
        return Colors.blue;
      case 'orange':
        return Colors.orange;
      default:
        return Colors.black;
    }
  }
}

/// IkanLelangLainGridItemViewModel - Data management class
/// Equivalent to IkanLelangLainGridItemViewModel in Swift
class IkanLelangLainGridItemViewModel extends ChangeNotifier {
  final IkanLelang _item;
  bool _isBookmarked = false;

  IkanLelangLainGridItemViewModel(this._item);

  // Getters
  IkanLelang get item => _item;
  bool get isBookmarked => _isBookmarked;

  // Methods
  void setBookmarked(bool bookmarked) {
    _isBookmarked = bookmarked;
    notifyListeners();
  }

  void toggleBookmark() {
    _isBookmarked = !_isBookmarked;
    notifyListeners();
  }

  String get formattedPrice {
    if (_item.opsiDeal == 1) {
      return 'Buy It Now';
    } else if (_item.viewLastValueBid.isNotEmpty) {
      return 'Bid: ${_item.viewLastValueBid}';
    } else {
      return 'OB: ${_item.ob}';
    }
  }

  Color get priceColor {
    if (_item.opsiDeal == 1) {
      return Colors.green;
    } else if (_item.colorLastValueBid.isNotEmpty) {
      return _getColorFromString(_item.colorLastValueBid);
    } else {
      return Colors.grey;
    }
  }

  Color _getColorFromString(String colorString) {
    switch (colorString.toLowerCase()) {
      case 'red':
        return Colors.red;
      case 'green':
        return Colors.green;
      case 'blue':
        return Colors.blue;
      case 'orange':
        return Colors.orange;
      default:
        return Colors.black;
    }
  }
}
