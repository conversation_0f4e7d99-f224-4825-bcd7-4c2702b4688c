import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../services/api_service.dart';
import '../../utils/colors.dart';
import '../../utils/preference_manager.dart';

/// SellerDashboardScreen - Complete seller analytics dashboard
/// Based on MasterKoiBot SellerDashboard.swift with enhanced features
class SellerDashboardScreen extends StatefulWidget {
  const SellerDashboardScreen({Key? key}) : super(key: key);

  @override
  State<SellerDashboardScreen> createState() => _SellerDashboardScreenState();
}

class _SellerDashboardScreenState extends State<SellerDashboardScreen> {
  bool _isLoading = true;
  bool _needLogin = false;

  // Dashboard data
  Map<String, dynamic> _dashboardData = {};
  Map<String, dynamic> _pointsData = {};

  // Profile data
  String _kodeSeller = '';
  String _namaMerchantSeller = '';
  String _viewSaldoDeposit = '';
  String _iconPointLevel = '';
  String _namaPointLevel = '';

  // Statistics
  String _jumlahIkanTerlelang = '0';
  String _jumlahIkanBelumTerlelang = '0';
  String _persenIkanTerlelang = '0';
  String _viewAvgNilaiIkanTerlelang = '0';
  String _jumlahIkanSudahDikirim = '0';
  String _jumlahIkanBelumDikirim = '0';
  String _jumlahIkanSudahDibayar = '0';
  String _jumlahIkanBelumDibayar = '0';

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    setState(() => _isLoading = true);

    try {
      // Load profile data from preferences
      _kodeSeller = await PreferenceManager.getString('kode_seller') ?? '';
      _namaMerchantSeller = await PreferenceManager.getString('nama_merchant_seller') ?? '';

      // Load dashboard data from API
      final dashboardResponse = await ApiService().getSellerDashboard();
      final pointsResponse = await ApiService().getPoints();

      if (dashboardResponse['meta']['action'] == 'login') {
        setState(() => _needLogin = true);
        return;
      }

      if (dashboardResponse['meta']['code'] == 200) {
        setState(() {
          _dashboardData = dashboardResponse['data'];
          _extractDashboardData();
        });
      }

      if (pointsResponse['statusCode'] == 200) {
        setState(() {
          _pointsData = pointsResponse['data'];
          _extractPointsData();
        });
      }

    } catch (e) {
      _showErrorDialog('Error loading dashboard: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _extractDashboardData() {
    _jumlahIkanTerlelang = _dashboardData['jumlah_ikan_terlelang']?.toString() ?? '0';
    _jumlahIkanBelumTerlelang = _dashboardData['jumlah_ikan_belum_terlelang']?.toString() ?? '0';
    _persenIkanTerlelang = _dashboardData['persen_ikan_terlelang']?.toString() ?? '0';
    _viewAvgNilaiIkanTerlelang = _dashboardData['view_avg_nilai_ikan_terlelang'] ?? '0';
    _jumlahIkanSudahDikirim = _dashboardData['jumlah_ikan_sudah_dikirim']?.toString() ?? '0';
    _jumlahIkanBelumDikirim = _dashboardData['jumlah_ikan_belum_dikirim']?.toString() ?? '0';
    _jumlahIkanSudahDibayar = _dashboardData['jumlah_ikan_sudah_dibayar']?.toString() ?? '0';
    _jumlahIkanBelumDibayar = _dashboardData['jumlah_ikan_belum_dibayar']?.toString() ?? '0';
    _viewSaldoDeposit = _dashboardData['view_saldo_deposit'] ?? '0';
  }

  void _extractPointsData() {
    _iconPointLevel = _pointsData['icon_point_level'] ?? '';
    _namaPointLevel = _pointsData['nama_point_level'] ?? '';
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dashboard Seller'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDashboardData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadDashboardData,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Profile Header
                    _buildProfileHeader(),
                    const SizedBox(height: 24),

                    // Statistics Cards
                    _buildStatisticsCards(),
                    const SizedBox(height: 24),

                    // Charts Section
                    _buildChartsSection(),
                    const SizedBox(height: 24),

                    // Quick Actions
                    _buildQuickActions(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildProfileHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primary, AppColors.primary.withOpacity(0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 30,
                backgroundColor: Colors.white,
                child: Text(
                  _kodeSeller.isNotEmpty ? _kodeSeller[0].toUpperCase() : 'S',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _kodeSeller,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      _namaMerchantSeller,
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.white70,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              const Icon(Icons.account_balance_wallet, color: Colors.green, size: 20),
              const SizedBox(width: 8),
              Text(
                'Deposit: $_viewSaldoDeposit',
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          if (_iconPointLevel.isNotEmpty) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                // You can add network image here for point level icon
                const Icon(Icons.star, color: Colors.amber, size: 20),
                const SizedBox(width: 8),
                Text(
                  _namaPointLevel,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }