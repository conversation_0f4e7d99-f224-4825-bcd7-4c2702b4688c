import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:masterkoi_app/config/flavor_config.dart';
import 'package:masterkoi_app/screens/fragments/rekpen_fragment.dart';
import 'package:masterkoi_app/screens/fragments/seller_fragment.dart';
import 'package:masterkoi_app/utils/app_utils.dart';
import '../widgets/app_drawer.dart';
import 'fragments/bidder_fragment.dart';
import 'fragments/ikan_lelang_fragment.dart';
import 'home_screen.dart';

class UtamaScreen extends StatefulWidget {
  @override
  State<UtamaScreen> createState() => _UtamaScreenState();
}

class _UtamaScreenState extends State<UtamaScreen> {
  int _selectedIndex = 2; // Default to Ikan Lelang tab
  bool _isSideMenuOpen = false;
  bool _showWebPage = false;

  late final List<Widget> _fragments;

  @override
  void initState() {
    super.initState();
    _initFragments();
  }

  void _initFragments() {
    _fragments = [
      HomeScreen(
        onSelectionChanged: _handleSelectionChanged,
        selection: _selectedIndex,
        webPage: _showWebPage,
      ),
      RekpenScreen(),
      IkanLelangFragment(),
      BidderFragment(),
      SellerFragment( ),
    ];
  }

  void _handleSelectionChanged(int index) {
    setState(() {
      if (index == 0) {
        // Show web page
        _showWebPage = true;
      } else {
        _showWebPage = false;
        _selectedIndex = index;
      }
      _initFragments(); // Rebuild fragments with new state
    });
  }

  void _toggleSideMenu() {
    setState(() {
      _isSideMenuOpen = !_isSideMenuOpen;
    });
  }

  Future<void> _shareApp() async {
    await AppUtils.shareApp();
  }

  Future<void> _rateApp() async {
    await AppUtils.rateApp();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        if (_showWebPage) {
          setState(() {
            _showWebPage = false;
            _selectedIndex = 2; // Return to default tab
            _initFragments();
          });
          return false;
        }
        return true;
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(FlavorConfig.instance.name),
          leading: IconButton(
            icon: const Icon(Icons.menu),
            onPressed: _toggleSideMenu,
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.star),
              onPressed: _rateApp,
            ),
            IconButton(
              icon: const Icon(Icons.share),
              onPressed: _shareApp,
            ),
          ],
        ),
        body: Stack(
          children: [
            _fragments[_selectedIndex],
            if (_isSideMenuOpen)
              GestureDetector(
                onTap: _toggleSideMenu,
                child: Container(
                  color: Colors.black54,
                ),
              ),
            AppDrawer(
              width: 270,
              isOpen: _isSideMenuOpen,
              onClose: _toggleSideMenu
            ),
          ],
        ),
        bottomNavigationBar: BottomNavigationBar(
          currentIndex: _selectedIndex,
          onTap: (index) {
            setState(() {
              _selectedIndex = index;
            });
          },
          type: BottomNavigationBarType.fixed,
          items: const [
            BottomNavigationBarItem(
              icon: FaIcon(FontAwesomeIcons.houseUser, size: 18),
              label: 'Home',
            ),
            BottomNavigationBarItem(
              icon: FaIcon(FontAwesomeIcons.creditCard, size: 18),
              label: 'Rekpen',
            ),
            BottomNavigationBarItem(
              icon: FaIcon(FontAwesomeIcons.fish, size: 20),
              label: 'Ikan Lelang',
            ),
            BottomNavigationBarItem(
              icon: FaIcon(FontAwesomeIcons.user, size: 18),
              label: 'Bidder',
            ),
            BottomNavigationBarItem(
              icon: FaIcon(FontAwesomeIcons.store, size: 18),
              label: 'Seller',
            ),
          ],
        ),
        floatingActionButton: MediaQuery.of(context).size.width < 600
            ? FloatingActionButton(
                onPressed: () {
                  setState(() {
                    _selectedIndex = 2; // Switch to Ikan Lelang
                  });
                },
                backgroundColor: Theme.of(context).primaryColor,
                child: const FaIcon(FontAwesomeIcons.fish),
              )
            : null,
        floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      ),
    );
  }
}
