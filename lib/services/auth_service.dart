import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../config/flavor_config.dart';
import '../models/user.dart';
import '../utils/constants.dart';

class AuthService {
  final String baseUrl = FlavorConfig.instance.baseUrl;

  // Fungsi untuk request OTP
  Future<Map<String, dynamic>> requestOtp(String phoneNumber) async {
    final response = await http.post(
      Uri.parse('$baseUrl/login/request-otp'),
      body: {'nomor_wa': phoneNumber},
    );

    if (response.statusCode == 200) {
      final jsonData = json.decode(response.body);

      if (jsonData['meta']['code'] == 200) {
        // Simpan nomor telepon untuk digunakan saat verifikasi OTP
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('nomor_wa', phoneNumber);

        return {'success': true, 'message': jsonData['meta']['message']};
      } else {
        return {'success': false, 'message': jsonData['meta']['message']};
      }
    } else {
      return {'success': false, 'message': 'Gagal terhubung ke server'};
    }
  }

  // Fungsi untuk login dengan OTP
  Future<Map<String, dynamic>> loginWithOtp(
    String otp, {
    String referal = '',
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final phoneNumber = prefs.getString('nomor_wa') ?? '';

      if (phoneNumber.isEmpty) {
        return {'success': false, 'message': 'Nomor telepon tidak ditemukan'};
      }

      // Pastikan otp adalah string
      final String otpString = otp.toString();

      final response = await http.post(
        Uri.parse('$baseUrl/login/with-otp'),
        body: {
          'nomor_wa': phoneNumber,
          'kode_otp': otpString,
          'referal': referal,
        },
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);

        if (jsonData['meta']['code'] == 200) {
          // Simpan session
          final prefs = await SharedPreferences.getInstance();
          final session = prefs.getString('session') ?? '';

          // Ambil data profil
          final profileData = await getProfile();

          return {
            'success': true,
            'message': jsonData['meta']['message'],
            'data': profileData,
          };
        } else {
          return {'success': false, 'message': jsonData['meta']['message']};
        }
      } else {
        return {
          'success': false,
          'message':
              'Gagal terhubung ke server. Status code: ${response.statusCode}',
        };
      }
    } catch (e) {
      return {'success': false, 'message': 'Error: ${e.toString()}'};
    }
  }

  // Fungsi untuk mendapatkan profil pengguna
  Future<Map<String, dynamic>> getProfile() async {
    final prefs = await SharedPreferences.getInstance();
    final session = prefs.getString('session') ?? '';


    if (session.isEmpty) {
      return {'success': false, 'message': 'Sesi tidak ditemukan'};
    }

    final response = await http.get(
      Uri.parse('$baseUrl/profil'),
      headers: {'session': session},
    );

    if (response.statusCode == 200) {
      final jsonData = json.decode(response.body);

      if (jsonData['meta']['action'] == 'login') {
        // Sesi habis, hapus sesi
        await logout();

        return {
          'success': false,
          'message': jsonData['meta']['message'],
          'action': 'login',
        };
      } else if (jsonData['meta']['code'] == 200) {
        // Simpan data profil
        final data = jsonData['data'];

        // Simpan data pengguna
        await prefs.setString('nama_users', data['nama_users'] ?? '');
        await prefs.setString('no_wa', data['no_wa'] ?? '');
        await prefs.setString('email', data['email'] ?? '');

        // Cek apakah pengguna adalah seller
        if (data.containsKey('seller') &&
            data['seller'] != null &&
            data['seller']['id_seller'].toString().isNotEmpty) {
          await prefs.setBool('is_seller', true);
          await prefs.setString(
            'kode_seller',
            data['seller']['kode_seller'] ?? '',
          );
          await prefs.setString(
            'nama_merchant_seller',
            data['seller']['nama_merchant_seller'] ?? '',
          );
          await prefs.setString(
            'view_url_foto_logo',
            data['seller']['view_url_foto_logo'] ?? '',
          );
          await prefs.setString(
            'list_seller',
            data['seller']['list_seller'] ?? '',
          );
        } else {
          await prefs.setBool('is_seller', false);
        }

        return {
          'success': true,
          'message': jsonData['meta']['message'],
          'data': data,
        };
      } else {
        return {'success': false, 'message': jsonData['meta']['message']};
      }
    } else {
      return {'success': false, 'message': 'Gagal terhubung ke server'};
    }
  }

  // Generic GET method for API requests
  Future<Map<String, dynamic>> get(String endpoint) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final session = prefs.getString('session') ?? '';

      final response = await http.get(
        Uri.parse('$baseUrl$endpoint'),
        headers: {
          'Session': session,
        },
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('Failed to load data');
      }
    } catch (e) {
      return {
        'meta': {
          'code': 500,
          'message': 'Failed to connect to server: ${e.toString()}'
        }
      };
    }
  }

  Future<Map<String, dynamic>> post(String endpoint, [Map<String, dynamic>? data]) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final session = prefs.getString('session') ?? '';


      final response = await http.post(
        Uri.parse('$baseUrl$endpoint'),
        headers: {
          'Session': session,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: data,
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('Failed to load data');
      }
    } catch (e) {
      return {
        'meta': {
          'code': 500,
          'message': 'Failed to connect to server: ${e.toString()}'
        }
      };
    }
  }

  // Fungsi untuk request PIN (lupa PIN)
  Future<Map<String, dynamic>> requestPin() async {
    final prefs = await SharedPreferences.getInstance();
    final session = prefs.getString('session') ?? '';


    if (session.isEmpty) {
      return {'success': false, 'message': 'Sesi tidak ditemukan'};
    }

    final response = await http.post(
      Uri.parse('$baseUrl/profil/request-pin'),
      headers: {'session': session},
    );

    if (response.statusCode == 200) {
      final jsonData = json.decode(response.body);

      if (jsonData['meta']['action'] == 'login') {
        // Sesi habis, hapus sesi
        await logout();

        return {
          'success': false,
          'message': jsonData['meta']['message'],
          'action': 'login',
        };
      } else if (jsonData['meta']['code'] == 200) {
        return {'success': true, 'message': jsonData['meta']['message']};
      } else {
        return {'success': false, 'message': jsonData['meta']['message']};
      }
    } else {
      return {'success': false, 'message': 'Gagal terhubung ke server'};
    }
  }

  // Fungsi untuk membuat atau mengubah PIN
  Future<Map<String, dynamic>> createOrChangePin({
    String? oldPin,
    required String newPin,
    required String confirmPin,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    final session = prefs.getString('session') ?? '';


    if (session.isEmpty) {
      return {'success': false, 'message': 'Sesi tidak ditemukan'};
    }

    final Map<String, String> body = {
      'new_pin': newPin,
      'confirm_pin': confirmPin,
    };

    // Jika oldPin tidak null, berarti ini adalah ubah PIN
    if (oldPin != null) {
      body['old_pin'] = oldPin;
    }

    final response = await http.post(
      Uri.parse('$baseUrl/profil/create-pin'),
      headers: {
        'session': session,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: body,
    );

    if (response.statusCode == 200) {
      final jsonData = json.decode(response.body);

      if (jsonData['meta']['action'] == 'login') {
        // Sesi habis, hapus sesi
        await logout();

        return {
          'success': false,
          'message': jsonData['meta']['message'],
          'action': 'login',
        };
      } else if (jsonData['meta']['code'] == 200) {
        return {'success': true, 'message': jsonData['meta']['message']};
      } else {
        return {'success': false, 'message': jsonData['meta']['message']};
      }
    } else {
      return {'success': false, 'message': 'Gagal terhubung ke server'};
    }
  }

  // Fungsi untuk verifikasi PIN
  Future<Map<String, dynamic>> verifyPin(String pin) async {
    final prefs = await SharedPreferences.getInstance();
    final session = prefs.getString('session') ?? '';


    if (session.isEmpty) {
      return {'success': false, 'message': 'Sesi tidak ditemukan'};
    }

    final response = await http.post(
      Uri.parse('$baseUrl${AppConstants.verifyPinEndpoint}'),
      headers: {
        'session': session,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: {'pin': pin},
    );

    if (response.statusCode == 200) {
      final jsonData = json.decode(response.body);

      if (jsonData['meta']['action'] == 'login') {
        await logout();
        return {
          'success': false,
          'message': jsonData['meta']['message'],
          'action': 'login',
        };
      } else if (jsonData['meta']['code'] == 200) {
        return {'success': true, 'message': jsonData['meta']['message']};
      } else {
        return {'success': false, 'message': jsonData['meta']['message']};
      }
    } else {
      return {'success': false, 'message': 'Gagal terhubung ke server'};
    }
  }

  // Fungsi untuk menyimpan FCM token
  Future<Map<String, dynamic>> saveFcmToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    final session = prefs.getString('session') ?? '';


    if (session.isEmpty) {
      return {'success': false, 'message': 'Sesi tidak ditemukan'};
    }

    final response = await http.post(
      Uri.parse('$baseUrl/api/profil/save-fcm'),
      headers: {
        'session': session,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: {'fcm_token': token},
    );

    if (response.statusCode == 200) {
      final jsonData = json.decode(response.body);

      if (jsonData['meta']['action'] == 'login') {
        await logout();
        return {
          'success': false,
          'message': jsonData['meta']['message'],
          'action': 'login',
        };
      } else if (jsonData['meta']['code'] == 200) {
        // Simpan token FCM di SharedPreferences
        await prefs.setString('fcm_token', token);
        return {'success': true, 'message': jsonData['meta']['message']};
      } else {
        return {'success': false, 'message': jsonData['meta']['message']};
      }
    } else {
      return {'success': false, 'message': 'Gagal terhubung ke server'};
    }
  }

  // Fungsi untuk logout
  Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('session');
    await prefs.remove('nama_users');
    await prefs.remove('no_wa');
    await prefs.remove('email');
    await prefs.remove('is_seller');
    await prefs.remove('kode_seller');
    await prefs.remove('nama_merchant_seller');
    await prefs.remove('view_url_foto_logo');
    await prefs.remove('list_seller');
    await prefs.remove('fcm_token');
    await prefs.remove('nomor_wa');
  }

  // Fungsi untuk mengecek apakah pengguna sudah login
  Future<bool> isLoggedIn() async {
    final prefs = await SharedPreferences.getInstance();
    final session = prefs.getString('session') ?? '';


    return session.isNotEmpty;
  }

  
  // Fungsi untuk mendapatkan informasi seller
  Future<Map<String, dynamic>> getSellerInfo() async {
    final prefs = await SharedPreferences.getInstance();
    final session = prefs.getString('session') ?? '';


    if (session.isEmpty) {
      return {'success': false, 'message': 'Sesi tidak ditemukan'};
    }

    final response = await http.get(
      Uri.parse('$baseUrl/api/seller/info'),
      headers: {'session': session},
    );

    if (response.statusCode == 200) {
      final jsonData = json.decode(response.body);

      if (jsonData['meta']['action'] == 'login') {
        await logout();
        return {
          'success': false,
          'message': jsonData['meta']['message'],
          'action': 'login',
        };
      } else if (jsonData['meta']['code'] == 200) {
        return {
          'success': true,
          'data': jsonData['data'],
        };
      } else {
        return {'success': false, 'message': jsonData['meta']['message']};
      }
    } else {
      return {'success': false, 'message': 'Gagal terhubung ke server'};
    }
  }
  
  // Fungsi untuk mendapatkan informasi bidder
  Future<Map<String, dynamic>> getBidderInfo() async {
    final prefs = await SharedPreferences.getInstance();
    final session = prefs.getString('session') ?? '';


    if (session.isEmpty) {
      return {'success': false, 'message': 'Sesi tidak ditemukan'};
    }

    final response = await http.get(
      Uri.parse('$baseUrl/api/bidder/info'),
      headers: {'session': session},
    );

    if (response.statusCode == 200) {
      final jsonData = json.decode(response.body);

      if (jsonData['meta']['action'] == 'login') {
        await logout();
        return {
          'success': false,
          'message': jsonData['meta']['message'],
          'action': 'login',
        };
      } else if (jsonData['meta']['code'] == 200) {
        return {
          'success': true,
          'data': jsonData['data'],
        };
      } else {
        return {'success': false, 'message': jsonData['meta']['message']};
      }
    } else {
      return {'success': false, 'message': 'Gagal terhubung ke server'};
    }
  }
}
