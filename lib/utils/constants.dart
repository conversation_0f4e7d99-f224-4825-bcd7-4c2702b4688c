class AppConstants {

  static const String developerUrl = 'http://neturmeric.com';
  static const int pageSize = 10;
  // API Endpoints
  static const String login = '/auth/login';
  static const String register = '/auth/register';
  static const String profile = '/profile';
  static const String ikanLelang = '/ikan-lelang';
  static const String filterOptions = '/filter-options';
  static const String favorites = '/favorites';
  static const String bids = '/bids';
  
  // Tambahan API Endpoints dari MasterKoiBot
  static const String ikanLiveEndpoint = '/api/ikan/live';
  static const String ikanLiveStatistikEndpoint = '/api/ikan/live/statistik';
  static const String ikanOpsiDealEndpoint = '/api/ikan/opsi-deal';
  static const String ikanDetailEndpoint = '/api/ikan/'; // + id_obyek_lelang
  static const String ikanSejenisEndpoint = '/api/ikan/'; // + id_obyek_lelang + '/ikan-lain-sejenis'
  static const String ikanPriorityEndpoint = '/api/ikan/priority';
  static const String ikanFavoriteEndpoint = '/api/ikan/favorite';
  static const String ikanRatingEndpoint = '/api/ikan/'; // + id_obyek_lelang + '/set-rating'
  
  static const String bidderHistoryEndpoint = '/api/bidder/history-bid';
  static const String bidderDashboardEndpoint = '/api/bidder/dashboard';
  static const String bidderIkanSayaEndpoint = '/api/bidder/ikan-saya/belum-ulasan';
  
  static const String sellerDashboardEndpoint = '/api/seller/dashboard';
  static const String sellerMostAuctionEndpoint = '/api/seller/member-most-auction';
  static const String sellerCsEndpoint = '/api/seller/cs';
  
  static const String pointsEndpoint = '/api/poin';
  static const String pointsHistoryEndpoint = '/api/poin/history';
  
  static const String rekpenAllEndpoint = '/api/rekpen/all';
  static const String rekpenKirimEndpoint = '/api/rekpen/kirim';
  static const String rekpenTerimaEndpoint = '/api/rekpen/terima';

  // Additional endpoints from API service
  static const String profileEndpoint = '/api/profil';
  static const String bidderSetEndpoint = '/api/bidder/set';
  static const String rekpenStatusEndpoint = '/api/rekpen/status-rekpen';
  static const String rekpenDetailEndpoint = '/api/rekpen/detail';
  static const String rekpenBayarEndpoint = '/api/rekpen/bayar';
  static const String verifyPinEndpoint = '/api/profil/verify-pin';

  // Shared Preferences Keys
  static const String tokenKey = 'auth_token';
  static const String userIdKey = 'user_id';
  static const String userDataKey = 'user_data';

  // Notification Channels
  static const String bidChannelId = 'bid_notifications';
  static const String bidChannelName = 'Bid Notifications';
  static const String bidChannelDescription = 'Notifications for auction bids';

  // Error Messages
  static const String networkError = 'Koneksi internet bermasalah';
  static const String serverError = 'Terjadi kesalahan pada server';
  static const String unknownError = 'Terjadi kesalahan';

  // Validation Rules
  static const int minPasswordLength = 6;
  static const int maxBidAttempts = 3;

  // Timeouts
  static const int connectionTimeout = 30; // seconds
  static const int receiveTimeout = 30; // seconds
}

