import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/colors.dart';

class FilterDialog extends StatefulWidget {
  final List<String> sizeIkanList;
  final List<String> levelSellerList;
  final List<String> modeLelangList;
  final List<String> lokasiIkanList;
  final Map<String, dynamic> currentFilters;
  final Function(Map<String, dynamic>) onApplyFilter;

  const FilterDialog({
    Key? key,
    required this.sizeIkanList,
    required this.levelSellerList,
    required this.modeLelangList,
    required this.lokasiIkanList,
    required this.currentFilters,
    required this.onApplyFilter,
  }) : super(key: key);

  @override
  State<FilterDialog> createState() => _FilterDialogState();
}

class _FilterDialogState extends State<FilterDialog> {
  late Map<String, dynamic> selectedFilters;

  // Price range controllers
  final TextEditingController _minPriceController = TextEditingController();
  final TextEditingController _maxPriceController = TextEditingController();

  // Rating filter
  double _minRating = 0.0;

  // Gender filter
  String? _selectedGender;

  // Status filter
  String? _selectedStatus;

  // Close timer filter
  String? _selectedCloseTimer;

  @override
  void initState() {
    super.initState();
    selectedFilters = Map.from(widget.currentFilters);

    // Initialize controllers with existing values
    _minPriceController.text = selectedFilters['min_price']?.toString() ?? '';
    _maxPriceController.text = selectedFilters['max_price']?.toString() ?? '';
    _minRating = selectedFilters['min_rating']?.toDouble() ?? 0.0;
    _selectedGender = selectedFilters['gender'];
    _selectedStatus = selectedFilters['status'];
    _selectedCloseTimer = selectedFilters['close_timer'];
  }

  @override
  void dispose() {
    _minPriceController.dispose();
    _maxPriceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Filter',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      setState(() {
                        selectedFilters.clear();
                      });
                    },
                    child: const Text('Reset'),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Size Filter
              if (widget.sizeIkanList.isNotEmpty) ...[
                const Text(
                  'Ukuran Ikan',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: widget.sizeIkanList.map((size) {
                    final isSelected = selectedFilters['size'] == size;
                    return FilterChip(
                      label: Text(size),
                      selected: isSelected,
                      onSelected: (selected) {
                        setState(() {
                          if (selected) {
                            selectedFilters['size'] = size;
                          } else {
                            selectedFilters.remove('size');
                          }
                        });
                      },
                      backgroundColor: Colors.grey[200],
                      selectedColor: AppColors.primary.withOpacity(0.2),
                      checkmarkColor: AppColors.primary,
                      labelStyle: TextStyle(
                        color: isSelected ? AppColors.primary : Colors.black87,
                      ),
                    );
                  }).toList(),
                ),
                const SizedBox(height: 16),
              ],

              // Seller Level Filter
              if (widget.levelSellerList.isNotEmpty) ...[
                const Text(
                  'Level Seller',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: widget.levelSellerList.map((level) {
                    final isSelected = selectedFilters['level_seller'] == level;
                    return FilterChip(
                      label: Text(level),
                      selected: isSelected,
                      onSelected: (selected) {
                        setState(() {
                          if (selected) {
                            selectedFilters['level_seller'] = level;
                          } else {
                            selectedFilters.remove('level_seller');
                          }
                        });
                      },
                      backgroundColor: Colors.grey[200],
                      selectedColor: AppColors.primary.withOpacity(0.2),
                      checkmarkColor: AppColors.primary,
                      labelStyle: TextStyle(
                        color: isSelected ? AppColors.primary : Colors.black87,
                      ),
                    );
                  }).toList(),
                ),
                const SizedBox(height: 16),
              ],

              // Mode Lelang Filter
              if (widget.modeLelangList.isNotEmpty) ...[
                const Text(
                  'Mode Lelang',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: widget.modeLelangList.map((mode) {
                    final isSelected = selectedFilters['mode_lelang'] == mode;
                    return FilterChip(
                      label: Text(mode),
                      selected: isSelected,
                      onSelected: (selected) {
                        setState(() {
                          if (selected) {
                            selectedFilters['mode_lelang'] = mode;
                          } else {
                            selectedFilters.remove('mode_lelang');
                          }
                        });
                      },
                      backgroundColor: Colors.grey[200],
                      selectedColor: AppColors.primary.withOpacity(0.2),
                      checkmarkColor: AppColors.primary,
                      labelStyle: TextStyle(
                        color: isSelected ? AppColors.primary : Colors.black87,
                      ),
                    );
                  }).toList(),
                ),
                const SizedBox(height: 16),
              ],

              // Location Filter
              if (widget.lokasiIkanList.isNotEmpty) ...[
                const Text(
                  'Lokasi Ikan',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: widget.lokasiIkanList.map((lokasi) {
                    final isSelected = selectedFilters['lokasi'] == lokasi;
                    return FilterChip(
                      label: Text(lokasi),
                      selected: isSelected,
                      onSelected: (selected) {
                        setState(() {
                          if (selected) {
                            selectedFilters['lokasi'] = lokasi;
                          } else {
                            selectedFilters.remove('lokasi');
                          }
                        });
                      },
                      backgroundColor: Colors.grey[200],
                      selectedColor: AppColors.primary.withOpacity(0.2),
                      checkmarkColor: AppColors.primary,
                      labelStyle: TextStyle(
                        color: isSelected ? AppColors.primary : Colors.black87,
                      ),
                    );
                  }).toList(),
                ),
                const SizedBox(height: 16),
              ],

              // Price Range Filter
              const Text(
                'Rentang Harga',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _minPriceController,
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      decoration: const InputDecoration(
                        labelText: 'Harga Min',
                        border: OutlineInputBorder(),
                        prefixText: 'Rp ',
                      ),
                      onChanged: (value) {
                        if (value.isNotEmpty) {
                          selectedFilters['min_price'] = int.tryParse(value) ?? 0;
                        } else {
                          selectedFilters.remove('min_price');
                        }
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextField(
                      controller: _maxPriceController,
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      decoration: const InputDecoration(
                        labelText: 'Harga Max',
                        border: OutlineInputBorder(),
                        prefixText: 'Rp ',
                      ),
                      onChanged: (value) {
                        if (value.isNotEmpty) {
                          selectedFilters['max_price'] = int.tryParse(value) ?? 0;
                        } else {
                          selectedFilters.remove('max_price');
                        }
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Rating Filter
              const Text(
                'Rating Seller Minimum',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: Slider(
                      value: _minRating,
                      min: 0.0,
                      max: 5.0,
                      divisions: 10,
                      label: _minRating.toStringAsFixed(1),
                      onChanged: (value) {
                        setState(() {
                          _minRating = value;
                          if (value > 0) {
                            selectedFilters['min_rating'] = value;
                          } else {
                            selectedFilters.remove('min_rating');
                          }
                        });
                      },
                    ),
                  ),
                  Text('${_minRating.toStringAsFixed(1)} ⭐'),
                ],
              ),
              const SizedBox(height: 16),

              // Gender Filter
              const Text(
                'Jenis Kelamin',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: ['Jantan', 'Betina', 'Tidak Diketahui'].map((gender) {
                  final isSelected = _selectedGender == gender;
                  return FilterChip(
                    label: Text(gender),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        if (selected) {
                          _selectedGender = gender;
                          selectedFilters['gender'] = gender;
                        } else {
                          _selectedGender = null;
                          selectedFilters.remove('gender');
                        }
                      });
                    },
                    backgroundColor: Colors.grey[200],
                    selectedColor: AppColors.primary.withOpacity(0.2),
                    checkmarkColor: AppColors.primary,
                    labelStyle: TextStyle(
                      color: isSelected ? AppColors.primary : Colors.black87,
                    ),
                  );
                }).toList(),
              ),
              const SizedBox(height: 16),

              // Status Bid Filter
              const Text(
                'Status Bid',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: ['Sudah Bid', 'Belum Bid', 'Semua'].map((status) {
                  final isSelected = _selectedStatus == status;
                  return FilterChip(
                    label: Text(status),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        if (selected) {
                          _selectedStatus = status;
                          selectedFilters['status'] = status;
                        } else {
                          _selectedStatus = null;
                          selectedFilters.remove('status');
                        }
                      });
                    },
                    backgroundColor: Colors.grey[200],
                    selectedColor: AppColors.primary.withOpacity(0.2),
                    checkmarkColor: AppColors.primary,
                    labelStyle: TextStyle(
                      color: isSelected ? AppColors.primary : Colors.black87,
                    ),
                  );
                }).toList(),
              ),
              const SizedBox(height: 16),

              // Close Timer Filter
              const Text(
                'Menjelang Tutup',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: ['5 Menit', '10 Menit', '30 Menit', '1 Jam'].map((timer) {
                  final isSelected = _selectedCloseTimer == timer;
                  return FilterChip(
                    label: Text(timer),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        if (selected) {
                          _selectedCloseTimer = timer;
                          selectedFilters['close_timer'] = timer;
                        } else {
                          _selectedCloseTimer = null;
                          selectedFilters.remove('close_timer');
                        }
                      });
                    },
                    backgroundColor: Colors.grey[200],
                    selectedColor: AppColors.primary.withOpacity(0.2),
                    checkmarkColor: AppColors.primary,
                    labelStyle: TextStyle(
                      color: isSelected ? AppColors.primary : Colors.black87,
                    ),
                  );
                }).toList(),
              ),

              const SizedBox(height: 24),

              // Apply Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    widget.onApplyFilter(selectedFilters);
                    Navigator.pop(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    'Terapkan Filter',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
