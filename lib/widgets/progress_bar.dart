import 'package:flutter/material.dart';
import '../utils/colors.dart';

/// A custom progress bar widget
/// Converted from Swift ProgressBarView
class ProgressBar extends StatelessWidget {
  final double size;
  final Color color;
  
  const ProgressBar({
    Key? key,
    this.size = 40.0,
    this.color = AppColors.primary,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        width: size,
        height: size,
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(color),
          strokeWidth: 3.0,
        ),
      ),
    );
  }
}