import 'package:flutter/material.dart';

class SortDialog extends StatelessWidget {
  final String currentSort;
  final Function(String) onSortSelected;

  const SortDialog({
    Key? key,
    required this.currentSort,
    required this.onSortSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Urutkan',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildSortOption(context, 'terbaru', 'Terbaru'),
            _buildSortOption(context, 'ob_terendah', 'OB Terendah'),
            _buildSortOption(context, 'ob_tertinggi', 'OB Tertinggi'),
            _buildSortOption(context, 'kb_terendah', 'KB Terendah'),
            _buildSortOption(context, 'kb_tertinggi', 'KB Tertinggi'),
            _buildSortOption(context, 'bid_terbanyak', 'Bid Terbanyak'),
            _buildSortOption(context, 'closing_time', 'Waktu Closing'),
          ],
        ),
      ),
    );
  }

  Widget _buildSortOption(BuildContext context, String value, String label) {
    return InkWell(
      onTap: () {
        onSortSelected(value);
        Navigator.pop(context);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          children: [
            Text(
              label,
              style: const TextStyle(fontSize: 16),
            ),
            const Spacer(),
            if (currentSort == value)
              const Icon(
                Icons.check,
                color: Colors.blue,
              ),
          ],
        ),
      ),
    );
  }
}
